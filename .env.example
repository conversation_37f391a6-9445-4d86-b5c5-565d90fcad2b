# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/flashbookkeeping?schema=public"

# 企业微信配置
WECOM_CORP_ID="your_corp_id"
WECOM_TOKEN="your_token"
WECOM_ENCODING_AES_KEY="your_encoding_aes_key"
WECOM_SECRET="your_secret"

# 豆包LLM配置
DOUBAO_API_KEY="your_doubao_api_key"
DOUBAO_API_ENDPOINT="https://ark.cn-beijing.volces.com/api/v3"

# Notion配置（公开集成）
# 在 https://www.notion.so/my-integrations 创建公开集成
# 设置重定向URL为: http://localhost:3000/notion/callback
NOTION_CLIENT_ID="your_notion_client_id"
NOTION_CLIENT_SECRET="your_notion_client_secret"
NOTION_REDIRECT_URI="http://localhost:3000/notion/callback"



# 应用配置
PORT=3000
NODE_ENV="development"

# HTTPS配置（可选，用于生产环境）
# 使用HTTPS时，请设置以下配置
HTTPS_ENABLED=false
HTTPS_PORT=3443
SSL_CERT_PATH="/path/to/cert.pem"
SSL_KEY_PATH="/path/to/key.pem"

# IP地址配置（用于IP直接访问）
# 设置为你的服务器IP地址
SERVER_IP="************"
SERVER_HOST="0.0.0.0"

# CORS配置
CORS_ORIGIN="https://flashbk.baiwotech.com"

# 信任代理（用于HTTPS和负载均衡）
TRUST_PROXY=true

# JWT配置
JWT_SECRET="your_jwt_secret_key"

# 加密配置
ENCRYPTION_KEY="32_character_long_encryption_key_here"

# RabbitMQ配置（用于消息队列）
RABBITMQ_URL="amqp://localhost:5672"
RABBITMQ_USER="guest"
RABBITMQ_PASSWORD="guest"
RABBITMQ_VHOST="/"

# 消息队列配置
QUEUE_RETRY_ATTEMPTS=3
QUEUE_RETRY_DELAY=2000
QUEUE_HEARTBEAT_INTERVAL=60
QUEUE_RECONNECT_TIME=5

# 支付配置
WECHAT_PAY_APP_ID="your_wechat_pay_app_id"
WECHAT_PAY_MCH_ID="your_wechat_pay_mch_id"
WECHAT_PAY_KEY="your_wechat_pay_key"

# 腾讯云配置（OCR和语音识别）
TENCENT_SECRET_ID="your_tencent_secret_id"
TENCENT_SECRET_KEY="your_tencent_secret_key"

# 阿里云配置（OCR和语音识别）
ALIYUN_OCR_APP_CODE="your_aliyun_ocr_app_code"
ALIYUN_VOICE_APP_KEY="your_aliyun_voice_app_key"
ALIYUN_VOICE_TOKEN="your_aliyun_voice_token"

# 百度语音配置
BAIDU_VOICE_APP_ID="your_baidu_voice_app_id"
BAIDU_VOICE_API_KEY="your_baidu_voice_api_key"
BAIDU_VOICE_SECRET_KEY="your_baidu_voice_secret_key"

# 日志配置
LOG_LEVEL="info"
LOG_FILE_PATH="./logs"
LOG_MAX_SIZE="20m"
LOG_MAX_FILES="30d"

# 免费用户限制
FREE_USER_MONTHLY_LIMIT=30