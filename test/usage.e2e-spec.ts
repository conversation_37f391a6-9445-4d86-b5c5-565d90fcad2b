import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';

describe('UsageController (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    prisma = moduleFixture.get<PrismaService>(PrismaService);
    await app.init();
  });

  afterAll(async () => {
    await prisma.$disconnect();
    await app.close();
  });

  beforeEach(async () => {
    // 清理测试数据
    await prisma.usageStat.deleteMany();
    await prisma.subscription.deleteMany();
    await prisma.user.deleteMany();
  });

  describe('/usage/:userId/current (GET)', () => {
    it('should return current usage for user with subscription', async () => {
      // 创建测试用户
      const user = await prisma.user.create({
        data: {
          id: 'test-user-1',
          wecom_user_id: 'wecom-test-1',
          name: 'Test User',
          state: 'active',
          usageCount: 0,
        },
      });

      // 创建订阅
      await prisma.subscription.create({
        data: {
          user_id: user.id,
          plan: 'monthly',
          limit: 1000,
          start_date: new Date(),
          end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          status: 'active',
        },
      });

      // 创建使用统计
      const currentYearMonth =
        new Date().getFullYear() * 100 + (new Date().getMonth() + 1);
      await prisma.usageStat.create({
        data: {
          user_id: user.id,
          year_month: currentYearMonth,
          count: 15,
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/usage/${user.id}/current`)
        .expect(200);

      expect(response.body.count).toBe(15);
      expect(response.body.limit).toBe(1000);
      expect(response.body.remaining).toBe(985);
      expect(response.body.percentage).toBe(1.5);
    });

    it('should return default usage for user without subscription', async () => {
      const user = await prisma.user.create({
        data: {
          id: 'test-user-2',
          wecom_user_id: 'wecom-test-2',
          name: 'Test User 2',
          state: 'active',
          usageCount: 0,
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/usage/${user.id}/current`)
        .expect(200);

      expect(response.body.count).toBe(0);
      expect(response.body.limit).toBe(30); // default free limit
      expect(response.body.remaining).toBe(30);
    });
  });

  describe('/usage/:userId/check-limit (GET)', () => {
    it('should return false when under limit', async () => {
      const user = await prisma.user.create({
        data: {
          id: 'test-user-3',
          wecom_user_id: 'wecom-test-3',
          name: 'Test User 3',
          state: 'active',
          usageCount: 0,
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/usage/${user.id}/check-limit`)
        .expect(200);

      expect(response.body.isOverLimit).toBe(false);
    });

    it('should return true when over limit', async () => {
      const user = await prisma.user.create({
        data: {
          id: 'test-user-4',
          wecom_user_id: 'wecom-test-4',
          name: 'Test User 4',
          state: 'active',
          usageCount: 0,
        },
      });

      // 创建超出限制的使用统计
      const currentYearMonth =
        new Date().getFullYear() * 100 + (new Date().getMonth() + 1);
      await prisma.usageStat.create({
        data: {
          user_id: user.id,
          year_month: currentYearMonth,
          count: 35, // 超过默认的30限制
        },
      });

      const response = await request(app.getHttpServer())
        .get(`/usage/${user.id}/check-limit`)
        .expect(200);

      expect(response.body.isOverLimit).toBe(true);
    });
  });

  describe('/usage/system/stats (GET)', () => {
    it('should return system statistics', async () => {
      // 创建一些测试数据
      const user1 = await prisma.user.create({
        data: {
          id: 'test-user-5',
          wecom_user_id: 'wecom-test-5',
          name: 'Test User 5',
          state: 'active',
          usageCount: 0,
        },
      });

      const user2 = await prisma.user.create({
        data: {
          id: 'test-user-6',
          wecom_user_id: 'wecom-test-6',
          name: 'Test User 6',
          state: 'active',
          usageCount: 0,
        },
      });

      await prisma.subscription.create({
        data: {
          user_id: user1.id,
          plan: 'monthly',
          limit: 1000,
          start_date: new Date(),
          end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          status: 'active',
        },
      });

      const currentYearMonth =
        new Date().getFullYear() * 100 + (new Date().getMonth() + 1);
      await prisma.usageStat.create({
        data: {
          user_id: user1.id,
          year_month: currentYearMonth,
          count: 10,
        },
      });

      const response = await request(app.getHttpServer())
        .get('/usage/system/stats')
        .expect(200);

      expect(response.body.totalUsers).toBe(2);
      expect(response.body.activeUsers).toBe(1);
      expect(response.body.totalUsage).toBe(10);
      expect(response.body.activeSubscriptions).toBe(1);
    });
  });
});
