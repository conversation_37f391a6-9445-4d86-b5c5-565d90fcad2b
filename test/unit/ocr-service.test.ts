import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';
import { OCRService, TencentOCRProvider, AliyunOCRProvider, BaiduOCRProvider } from '../src/modules/ai/ocr.service';

// Mock fs
jest.mock('fs', () => ({
  readFileSync: jest.fn().mockReturnValue(Buffer.from('mock-image-data')),
  createReadStream: jest.fn().mockReturnValue({ pipe: jest.fn() }),
}));

// Mock FormData
jest.mock('form-data', () => {
  return jest.fn().mockImplementation(() => ({
    append: jest.fn(),
    getHeaders: jest.fn().mockReturnValue({ 'content-type': 'multipart/form-data' }),
  }));
});

describe('OCR Service Unit Tests', () => {
  let ocrService: OCRService;
  let tencentProvider: TencentOCRProvider;
  let aliyunProvider: AliyunOCRProvider;
  let baiduProvider: BaiduOCRProvider;
  let configService: ConfigService;
  let httpService: HttpService;

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockHttpService = {
    post: jest.fn(),
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OCRService,
        TencentOCRProvider,
        AliyunOCRProvider,
        BaiduOCRProvider,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: HttpService, useValue: mockHttpService },
      ],
    }).compile();

    ocrService = module.get<OCRService>(OCRService);
    tencentProvider = module.get<TencentOCRProvider>(TencentOCRProvider);
    aliyunProvider = module.get<AliyunOCRProvider>(AliyunOCRProvider);
    baiduProvider = module.get<BaiduOCRProvider>(BaiduOCRProvider);
    configService = module.get<ConfigService>(ConfigService);
    httpService = module.get<HttpService>(HttpService);

    jest.clearAllMocks();
  });

  describe('OCRService', () => {
    it('should recognize text using tencent provider', async () => {
      const mockResponse = {
        data: {
          TextDetections: [
            { DetectedText: '餐饮发票 ￥58.50', Confidence: 95 },
            { DetectedText: '2024-01-15 星巴克咖啡', Confidence: 90 },
          ],
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const result = await ocrService.recognize('/test/image.jpg', 'tencent');

      expect(result.text).toBe('餐饮发票 ￥58.50\n2024-01-15 星巴克咖啡');
      expect(result.confidence).toBe(95);
      expect(result.extractedFields.amount).toBe('58.50');
      expect(result.extractedFields.date).toBe('2024-01-15');
      expect(result.extractedFields.vendor).toBe('星巴克咖啡');
    });

    it('should recognize text using aliyun provider', async () => {
      const mockResponse = {
        data: {
          prism_wordsInfo: [
            { word: '餐饮发票', confidence: 95 },
            { word: '￥58.50', confidence: 98 },
            { word: '2024-01-15', confidence: 90 },
            { word: '星巴克咖啡', confidence: 92 },
          ],
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const result = await ocrService.recognize('/test/image.jpg', 'aliyun');

      expect(result.text).toBe('餐饮发票\n￥58.50\n2024-01-15\n星巴克咖啡');
      expect(result.confidence).toBe(95);
      expect(result.extractedFields.amount).toBe('58.50');
    });

    it('should recognize text using baidu provider', async () => {
      const mockResponse = {
        data: {
          words_result: [
            { words: '餐饮发票', probability: { average: 0.95 } },
            { words: '￥58.50', probability: { average: 0.98 } },
            { words: '2024-01-15', probability: { average: 0.90 } },
            { words: '星巴克咖啡', probability: { average: 0.92 } },
          ],
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));
      mockHttpService.get.mockReturnValue(of({ data: { access_token: 'mock-token' } }));

      const result = await ocrService.recognize('/test/image.jpg', 'baidu');

      expect(result.text).toBe('餐饮发票\n￥58.50\n2024-01-15\n星巴克咖啡');
      expect(result.confidence).toBe(95);
      expect(result.extractedFields.amount).toBe('58.50');
    });

    it('should throw error for unsupported provider', async () => {
      await expect(
        ocrService.recognize('/test/image.jpg', 'unsupported-provider'),
      ).rejects.toThrow('不支持的OCR提供商: unsupported-provider');
    });

    it('should handle API errors gracefully', async () => {
      mockHttpService.post.mockReturnValue(throwError(() => new Error('API Error')));

      await expect(
        ocrService.recognize('/test/image.jpg', 'tencent'),
      ).rejects.toThrow('OCR识别失败: API Error');
    });

    it('should perform batch OCR recognition', async () => {
      const mockResponse = {
        data: {
          TextDetections: [
            { DetectedText: '餐饮发票 ￥58.50', Confidence: 95 },
          ],
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const imagePaths = ['/test/image1.jpg', '/test/image2.jpg'];
      const results = await ocrService.batchRecognize(imagePaths, 'tencent');

      expect(results).toHaveLength(2);
      expect(results[0].text).toBe('餐饮发票 ￥58.50');
      expect(results[1].text).toBe('餐饮发票 ￥58.50');
    });

    it('should return supported providers', () => {
      const providers = ocrService.getSupportedProviders();
      expect(providers).toEqual(['tencent', 'aliyun', 'baidu']);
    });
  });

  describe('Field Extraction', () => {
    it('should extract amount from text', () => {
      const text = '餐饮发票 ￥58.50 2024-01-15 星巴克咖啡';
      const extracted = tencentProvider['extractFieldsFromText'](text);
      expect(extracted.amount).toBe('58.50');
    });

    it('should extract date from text', () => {
      const text = '发票日期：2024-01-15 星巴克咖啡 ￥58.50';
      const extracted = tencentProvider['extractFieldsFromText'](text);
      expect(extracted.date).toBe('2024-01-15');
    });

    it('should extract vendor from text', () => {
      const text = '餐饮发票 ￥58.50 星巴克咖啡 2024-01-15';
      const extracted = tencentProvider['extractFieldsFromText'](text);
      expect(extracted.vendor).toBe('星巴克咖啡');
    });

    it('should extract category from text', () => {
      const text = '快餐店发票 ￥25.00 麦当劳 2024-01-15';
      const extracted = tencentProvider['extractFieldsFromText'](text);
      expect(extracted.category).toBe('餐饮');
    });

    it('should extract invoice number from text', () => {
      const text = '发票号码：INV-2024-001 金额：￥100.00';
      const extracted = tencentProvider['extractFieldsFromText'](text);
      expect(extracted.invoiceNumber).toBe('INV-2024-001');
    });

    it('should extract tax number from text', () => {
      const text = '税号：91310000123456789X 金额：￥100.00';
      const extracted = tencentProvider['extractFieldsFromText'](text);
      expect(extracted.taxNumber).toBe('91310000123456789X');
    });
  });

  describe('Configuration', () => {
    it('should throw error when tencent config is missing', async () => {
      mockConfigService.get.mockReturnValue(undefined);

      await expect(
        tencentProvider.recognize({ imagePath: '/test/image.jpg' }),
      ).rejects.toThrow('腾讯云OCR配置不完整');
    });

    it('should throw error when aliyun config is missing', async () => {
      mockConfigService.get.mockReturnValue(undefined);

      await expect(
        aliyunProvider.recognize({ imagePath: '/test/image.jpg' }),
      ).rejects.toThrow('阿里云OCR配置不完整');
    });

    it('should throw error when baidu config is missing', async () => {
      mockConfigService.get.mockReturnValue(undefined);

      await expect(
        baiduProvider.recognize({ imagePath: '/test/image.jpg' }),
      ).rejects.toThrow('百度OCR配置不完整');
    });
  });
});