import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { of, throwError } from 'rxjs';
import { VoiceToTextService, TencentVoiceProvider, AliyunVoiceProvider, BaiduVoiceProvider } from '../src/modules/ai/voice-to-text.service';

// Mock fs
jest.mock('fs', () => ({
  readFileSync: jest.fn().mockReturnValue(Buffer.from('mock-audio-data')),
  statSync: jest.fn().mockReturnValue({ size: 1024 }),
  copyFileSync: jest.fn(),
}));

// Mock FormData
jest.mock('form-data', () => {
  return jest.fn().mockImplementation(() => ({
    append: jest.fn(),
    getHeaders: jest.fn().mockReturnValue({ 'content-type': 'multipart/form-data' }),
  }));
});

describe('Voice To Text Service Unit Tests', () => {
  let voiceService: VoiceToTextService;
  let tencentProvider: TencentVoiceProvider;
  let aliyunProvider: AliyunVoiceProvider;
  let baiduProvider: BaiduVoiceProvider;
  let configService: ConfigService;
  let httpService: HttpService;

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockHttpService = {
    post: jest.fn(),
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VoiceToTextService,
        TencentVoiceProvider,
        AliyunVoiceProvider,
        BaiduVoiceProvider,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: HttpService, useValue: mockHttpService },
      ],
    }).compile();

    voiceService = module.get<VoiceToTextService>(VoiceToTextService);
    tencentProvider = module.get<TencentVoiceProvider>(TencentVoiceProvider);
    aliyunProvider = module.get<AliyunVoiceProvider>(AliyunVoiceProvider);
    baiduProvider = module.get<BaiduVoiceProvider>(BaiduVoiceProvider);
    configService = module.get<ConfigService>(ConfigService);
    httpService = module.get<HttpService>(HttpService);

    jest.clearAllMocks();
  });

  describe('VoiceToTextService', () => {
    it('should transcribe audio using tencent provider', async () => {
      const mockResponse = {
        data: {
          Response: {
            Result: '今天在公司楼下吃了午饭，金额是25元',
            SentenceList: [
              {
                StartTime: 0,
                EndTime: 3,
                FinalSentence: '今天在公司楼下吃了午饭',
                WordSize: 10,
              },
              {
                StartTime: 3,
                EndTime: 6,
                FinalSentence: '金额是25元',
                WordSize: 5,
              },
            ],
          },
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const result = await voiceService.transcribe('/test/audio.mp3', 'tencent');

      expect(result.text).toBe('今天在公司楼下吃了午饭，金额是25元');
      expect(result.confidence).toBe(0.9);
      expect(result.segments).toHaveLength(2);
      expect(result.segments?.[0].text).toBe('今天在公司楼下吃了午饭');
    });

    it('should transcribe audio using aliyun provider', async () => {
      const mockResponse = {
        data: {
          status: 20000000,
          result: '今天在公司楼下吃了午饭，金额是25元',
          words: [
            { begin_time: 0, end_time: 3, word: '今天在公司楼下吃了午饭', confidence: 0.95 },
            { begin_time: 3, end_time: 6, word: '金额是25元', confidence: 0.92 },
          ],
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const result = await voiceService.transcribe('/test/audio.mp3', 'aliyun');

      expect(result.text).toBe('今天在公司楼下吃了午饭，金额是25元');
      expect(result.confidence).toBe(0.9);
      expect(result.segments).toHaveLength(2);
      expect(result.segments?.[0].text).toBe('今天在公司楼下吃了午饭');
    });

    it('should transcribe audio using baidu provider', async () => {
      const mockResponse = {
        data: {
          err_no: 0,
          result: ['今天在公司楼下吃了午饭，金额是25元'],
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));
      mockHttpService.get.mockReturnValue(of({ data: { access_token: 'mock-token' } }));

      const result = await voiceService.transcribe('/test/audio.mp3', 'baidu');

      expect(result.text).toBe('今天在公司楼下吃了午饭，金额是25元');
      expect(result.confidence).toBe(0.9);
      expect(result.segments).toBeUndefined(); // 百度标准API不返回分段信息
    });

    it('should throw error for unsupported provider', async () => {
      await expect(
        voiceService.transcribe('/test/audio.mp3', 'unsupported-provider'),
      ).rejects.toThrow('不支持的语音提供商: unsupported-provider');
    });

    it('should handle API errors gracefully', async () => {
      mockHttpService.post.mockReturnValue(throwError(() => new Error('API Error')));

      await expect(
        voiceService.transcribe('/test/audio.mp3', 'tencent'),
      ).rejects.toThrow('语音识别失败: API Error');
    });

    it('should perform batch transcription', async () => {
      const mockResponse = {
        data: {
          Response: {
            Result: '测试语音文本',
          },
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const audioPaths = ['/test/audio1.mp3', '/test/audio2.mp3'];
      const results = await voiceService.batchTranscribe(audioPaths, 'tencent');

      expect(results).toHaveLength(2);
      expect(results[0].text).toBe('测试语音文本');
      expect(results[1].text).toBe('测试语音文本');
    });

    it('should return supported providers', () => {
      const providers = voiceService.getSupportedProviders();
      expect(providers).toEqual(['tencent', 'aliyun', 'baidu']);
    });
  });

  describe('Audio Validation', () => {
    it('should validate valid audio file', async () => {
      const result = await voiceService.validateAudioFile('/test/audio.mp3');

      expect(result.isValid).toBe(true);
      expect(result.format).toBe('mp3');
      expect(result.sampleRate).toBe(16000);
    });

    it('should reject empty audio file', async () => {
      const mockFs = require('fs');
      mockFs.statSync.mockReturnValue({ size: 0 });

      const result = await voiceService.validateAudioFile('/test/audio.mp3');

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('音频文件为空');
    });

    it('should reject large audio file', async () => {
      const mockFs = require('fs');
      mockFs.statSync.mockReturnValue({ size: 15 * 1024 * 1024 }); // 15MB

      const result = await voiceService.validateAudioFile('/test/audio.mp3');

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('音频文件过大，最大支持10MB');
    });

    it('should reject unsupported audio format', async () => {
      const result = await voiceService.validateAudioFile('/test/audio.ogg');

      expect(result.isValid).toBe(false);
      expect(result.error).toContain('不支持的音频格式');
    });
  });

  describe('Configuration', () => {
    it('should throw error when tencent config is missing', async () => {
      mockConfigService.get.mockReturnValue(undefined);

      await expect(
        tencentProvider.transcribe({ audioPath: '/test/audio.mp3' }),
      ).rejects.toThrow('腾讯云语音配置不完整');
    });

    it('should throw error when aliyun config is missing', async () => {
      mockConfigService.get.mockReturnValue(undefined);

      await expect(
        aliyunProvider.transcribe({ audioPath: '/test/audio.mp3' }),
      ).rejects.toThrow('阿里云语音配置不完整');
    });

    it('should throw error when baidu config is missing', async () => {
      mockConfigService.get.mockReturnValue(undefined);

      await expect(
        baiduProvider.transcribe({ audioPath: '/test/audio.mp3' }),
      ).rejects.toThrow('百度语音配置不完整');
    });

    it('should handle baidu access token error', async () => {
      mockHttpService.get.mockReturnValue(of({ data: { error: 'invalid_client', error_description: 'Invalid client' } }));

      await expect(
        baiduProvider.transcribe({ audioPath: '/test/audio.mp3' }),
      ).rejects.toThrow('获取百度token失败: Invalid client');
    });

    it('should handle tencent API error', async () => {
      const mockResponse = {
        data: {
          Response: {
            Error: {
              Code: 'InvalidParameter',
              Message: 'Invalid audio format',
            },
          },
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      await expect(
        tencentProvider.transcribe({ audioPath: '/test/audio.mp3' }),
      ).rejects.toThrow('语音识别失败: Invalid audio format');
    });

    it('should handle aliyun API error', async () => {
      const mockResponse = {
        data: {
          status: 40000001,
          status_text: 'Invalid parameter',
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      await expect(
        aliyunProvider.transcribe({ audioPath: '/test/audio.mp3' }),
      ).rejects.toThrow('语音识别失败: Invalid parameter');
    });

    it('should handle baidu API error', async () => {
      const mockResponse = {
        data: {
          err_no: 3301,
          err_msg: 'Speech quality error',
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      await expect(
        baiduProvider.transcribe({ audioPath: '/test/audio.mp3' }),
      ).rejects.toThrow('语音识别失败: Speech quality error');
    });
  });
});