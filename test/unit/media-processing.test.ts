import { Test, TestingModule } from '@nestjs/testing';
import { MediaProcessor } from '../src/modules/queue/processors/media-processor.service';
import { WecomMediaService } from '../src/modules/wecom/wecom-media.service';
import { OCRService } from '../src/modules/ai/ocr.service';
import { VoiceToTextService } from '../src/modules/ai/voice-to-text.service';
import { AiService } from '../src/modules/ai/ai.service';

// Mock services
const mockWecomMediaService = {
  downloadMedia: jest.fn(),
  cleanupExpiredMedia: jest.fn(),
};

const mockOCRService = {
  recognize: jest.fn(),
};

const mockVoiceToTextService = {
  transcribe: jest.fn(),
  validateAudioFile: jest.fn(),
};

const mockAiService = {
  extractBillingInfo: jest.fn(),
};

describe('Media Processing Unit Tests', () => {
  let mediaProcessor: MediaProcessor;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MediaProcessor,
        { provide: WecomMediaService, useValue: mockWecomMediaService },
        { provide: OCRService, useValue: mockOCRService },
        { provide: VoiceToTextService, useValue: mockVoiceToTextService },
        { provide: AiService, useValue: mockAiService },
      ],
    }).compile();

    mediaProcessor = module.get<MediaProcessor>(MediaProcessor);
    jest.clearAllMocks();
  });

  describe('Image Processing', () => {
    it('should process image successfully', async () => {
      const mockDownloadResult = {
        filePath: '/test/image.jpg',
        fileName: 'test.jpg',
        fileSize: 1024,
        mimeType: 'image/jpeg',
        mediaId: 'test-media-id',
      };

      const mockOcrResult = {
        text: '餐饮发票 ￥58.50 2024-01-15 星巴克咖啡',
        confidence: 0.95,
        extractedFields: { amount: '58.50', date: '2024-01-15', vendor: '星巴克咖啡' },
        rawData: {},
      };

      const mockBillingInfo = {
        amount: 58.5,
        category: '餐饮',
        date: '2024-01-15',
        description: '星巴克咖啡',
        type: 'expense',
        error: null,
      };

      mockWecomMediaService.downloadMedia.mockResolvedValue(mockDownloadResult);
      mockOCRService.recognize.mockResolvedValue(mockOcrResult);
      mockAiService.extractBillingInfo.mockResolvedValue(mockBillingInfo);

      const result = await mediaProcessor.processImage({
        mediaId: 'test-media-id',
        messageType: 'image',
        userId: 'test-user-id',
      });

      expect(result).toEqual({
        mediaId: 'test-media-id',
        filePath: '/test/image.jpg',
        extractedText: '餐饮发票 ￥58.50 2024-01-15 星巴克咖啡',
        billingInfo: mockBillingInfo,
        processingType: 'image',
      });

      expect(mockWecomMediaService.downloadMedia).toHaveBeenCalledWith(
        'test-media-id',
        undefined,
        undefined,
      );
      expect(mockOCRService.recognize).toHaveBeenCalledWith('/test/image.jpg', 'tencent');
      expect(mockAiService.extractBillingInfo).toHaveBeenCalledWith('餐饮发票 ￥58.50 2024-01-15 星巴克咖啡');
    });

    it('should handle image processing failure', async () => {
      mockWecomMediaService.downloadMedia.mockRejectedValue(new Error('Download failed'));

      await expect(
        mediaProcessor.processImage({
          mediaId: 'test-media-id',
          messageType: 'image',
          userId: 'test-user-id',
        }),
      ).rejects.toThrow('Download failed');
    });
  });

  describe('Voice Processing', () => {
    it('should process voice successfully', async () => {
      const mockDownloadResult = {
        filePath: '/test/voice.mp3',
        fileName: 'test.mp3',
        fileSize: 2048,
        mimeType: 'audio/mpeg',
        mediaId: 'test-media-id',
      };

      const mockValidation = {
        isValid: true,
        format: 'mp3',
        duration: 15,
        sampleRate: 16000,
      };

      const mockVoiceResult = {
        text: '今天在公司楼下吃了午饭，金额是25元',
        confidence: 0.85,
        segments: [],
        rawData: {},
      };

      const mockBillingInfo = {
        amount: 25,
        category: '餐饮',
        date: '2024-01-15',
        description: '公司楼下午饭',
        type: 'expense',
        error: null,
      };

      mockWecomMediaService.downloadMedia.mockResolvedValue(mockDownloadResult);
      mockVoiceToTextService.validateAudioFile.mockResolvedValue(mockValidation);
      mockVoiceToTextService.transcribe.mockResolvedValue(mockVoiceResult);
      mockAiService.extractBillingInfo.mockResolvedValue(mockBillingInfo);

      const result = await mediaProcessor.processVoice({
        mediaId: 'test-media-id',
        messageType: 'voice',
        userId: 'test-user-id',
      });

      expect(result).toEqual({
        mediaId: 'test-media-id',
        filePath: '/test/voice.mp3',
        extractedText: '今天在公司楼下吃了午饭，金额是25元',
        billingInfo: mockBillingInfo,
        processingType: 'voice',
      });
    });

    it('should handle invalid audio file', async () => {
      const mockDownloadResult = {
        filePath: '/test/voice.mp3',
        fileName: 'test.mp3',
        fileSize: 0,
        mimeType: 'audio/mpeg',
        mediaId: 'test-media-id',
      };

      mockWecomMediaService.downloadMedia.mockResolvedValue(mockDownloadResult);
      mockVoiceToTextService.validateAudioFile.mockResolvedValue({
        isValid: false,
        error: '空文件',
      });

      await expect(
        mediaProcessor.processVoice({
          mediaId: 'test-media-id',
          messageType: 'voice',
          userId: 'test-user-id',
        }),
      ).rejects.toThrow('音频文件验证失败: 空文件');
    });
  });

  describe('File Processing', () => {
    it('should process image file as image', async () => {
      const mockDownloadResult = {
        filePath: '/test/document.jpg',
        fileName: 'document.jpg',
        fileSize: 1024,
        mimeType: 'image/jpeg',
        mediaId: 'test-media-id',
      };

      const mockOcrResult = {
        text: '办公用品 ￥120.00 2024-01-15 文具店',
        confidence: 0.9,
        extractedFields: {},
        rawData: {},
      };

      const mockBillingInfo = {
        amount: 120,
        category: '办公',
        date: '2024-01-15',
        description: '文具店购买办公用品',
        type: 'expense',
        error: null,
      };

      mockWecomMediaService.downloadMedia.mockResolvedValue(mockDownloadResult);
      mockOCRService.recognize.mockResolvedValue(mockOcrResult);
      mockAiService.extractBillingInfo.mockResolvedValue(mockBillingInfo);

      const result = await mediaProcessor.processFile({
        mediaId: 'test-media-id',
        messageType: 'file',
        userId: 'test-user-id',
      });

      expect(result.processingType).toBe('file');
      expect(result.extractedText).toBe('办公用品 ￥120.00 2024-01-15 文具店');
      expect(mockOCRService.recognize).toHaveBeenCalled();
    });
  });

  describe('Batch Processing', () => {
    it('should handle batch media processing', async () => {
      const jobs = [
        { mediaId: 'image1', messageType: 'image', userId: 'user1' },
        { mediaId: 'voice1', messageType: 'voice', userId: 'user1' },
        { mediaId: 'file1', messageType: 'file', userId: 'user1' },
      ];

      const mockDownloadResult = {
        filePath: '/test/file',
        fileName: 'test.file',
        fileSize: 1024,
        mimeType: 'image/jpeg',
        mediaId: 'test-media-id',
      };

      mockWecomMediaService.downloadMedia.mockResolvedValue(mockDownloadResult);
      mockOCRService.recognize.mockResolvedValue({
        text: '测试文本',
        confidence: 0.9,
        extractedFields: {},
        rawData: {},
      });
      mockVoiceToTextService.validateAudioFile.mockResolvedValue({
        isValid: true,
        format: 'mp3',
        duration: 10,
        sampleRate: 16000,
      });
      mockVoiceToTextService.transcribe.mockResolvedValue({
        text: '测试语音文本',
        confidence: 0.85,
        segments: [],
        rawData: {},
      });
      mockAiService.extractBillingInfo.mockResolvedValue({
        amount: 100,
        category: '其他',
        date: '2024-01-15',
        description: '测试描述',
        type: 'expense',
        error: null,
      });

      const result = await mediaProcessor.batchProcessMedia(jobs);

      expect(result.success).toBe(3);
      expect(result.failed).toBe(0);
      expect(result.results).toHaveLength(3);
    });
  });

  describe('Utility Functions', () => {
    it('should calculate priority correctly', () => {
      expect(mediaProcessor.calculatePriority({
        mediaId: 'test',
        messageType: 'image',
        userId: 'test',
      })).toBe(3);

      expect(mediaProcessor.calculatePriority({
        mediaId: 'test',
        messageType: 'voice',
        userId: 'test',
      })).toBe(2);

      expect(mediaProcessor.calculatePriority({
        mediaId: 'test',
        messageType: 'file',
        userId: 'test',
      })).toBe(1);
    });

    it('should return supported media types', () => {
      const types = mediaProcessor.getSupportedMediaTypes();
      expect(types).toEqual(['image', 'voice', 'file']);
    });
  });
});