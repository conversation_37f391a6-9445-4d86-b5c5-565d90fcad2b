#!/bin/bash

# 为IP地址生成自签名SSL证书
# 使用方法: ./scripts/generate-ssl-ip.sh ************

IP=${1:-"************"}
CERT_DIR="./ssl"

# 创建SSL目录
mkdir -p $CERT_DIR

# 生成私钥
openssl genrsa -out "$CERT_DIR/server.key" 2048

# 生成证书签名请求
openssl req -new -key "$CERT_DIR/server.key" -out "$CERT_DIR/server.csr" -subj "/C=CN/ST=Beijing/L=Beijing/O=FlashBookkeeping/CN=$IP"

# 生成自签名证书
openssl x509 -req -days 365 -in "$CERT_DIR/server.csr" -signkey "$CERT_DIR/server.key" -out "$CERT_DIR/server.crt" -extfile <(echo "subjectAltName=IP:$IP")

# 清理CSR文件
rm "$CERT_DIR/server.csr"

echo "✅ SSL证书已生成:"
echo "  私钥: $CERT_DIR/server.key"
echo "  证书: $CERT_DIR/server.crt"
echo ""
echo "🔧 请更新 .env 文件:"
echo "  HTTPS_ENABLED=true"
echo "  SSL_CERT_PATH=./ssl/server.crt"
echo "  SSL_KEY_PATH=./ssl/server.key"
echo "  HTTPS_PORT=3443"