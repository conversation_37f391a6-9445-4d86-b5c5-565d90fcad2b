# FlashBookkeeping API 文档

## 概述

FlashBookkeeping（闪电记账）是一个基于企业微信的智能记账应用，通过AI识别用户的记账信息并同步到Notion等第三方平台。

## 基础信息

- **基础URL**: `http://localhost:3000`
- **API文档**: `http://localhost:3000/api`
- **版本**: 1.0.0

## 认证

目前API主要通过企业微信用户ID进行身份识别，暂未实现JWT认证。

## 核心接口

### 1. 健康检查

#### GET /health
检查应用是否正常运行

**响应示例**:
```json
{
  "status": "ok",
  "timestamp": "2023-07-12T10:00:00.000Z",
  "uptime": 3600,
  "memory": {
    "rss": 50331648,
    "heapTotal": 20971520,
    "heapUsed": 15728640
  }
}
```

#### GET /health/db
检查数据库连接状态

#### GET /health/detailed
详细的系统健康检查

### 2. 企业微信接口

#### GET /wecom/callback
验证企业微信回调URL

**参数**:
- `msg_signature`: 消息签名
- `timestamp`: 时间戳
- `nonce`: 随机数
- `echostr`: 验证字符串

#### POST /wecom/callback
处理企业微信消息回调

**参数**:
- `msg_signature`: 消息签名
- `timestamp`: 时间戳
- `nonce`: 随机数

**请求体**: 加密的XML消息

### 3. AI服务

#### POST /ai/extract-billing
提取账单信息

**请求体**:
```json
{
  "type": "text",
  "content": "午餐 25元 麦当劳",
  "url": "可选的媒体URL",
  "mimeType": "可选的文件类型"
}
```

**响应示例**:
```json
{
  "amount": 25,
  "category": "餐饮",
  "date": "2023-07-12",
  "description": "午餐 麦当劳",
  "confidence": 0.9
}
```

#### POST /ai/validate-billing
验证账单信息

### 4. 记账业务

#### POST /billing/process
处理记账请求

**请求体**:
```json
{
  "userId": "user123",
  "messageContent": {
    "type": "text",
    "content": "午餐 25元 麦当劳"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "记账成功",
  "billingInfo": {
    "amount": 25,
    "category": "餐饮",
    "date": "2023-07-12",
    "description": "午餐 麦当劳"
  }
}
```

#### GET /billing/binding-status/:userId
获取用户绑定状态

#### POST /billing/handle-command
处理绑定指令

### 5. Notion集成

#### GET /notion/auth-url
获取Notion OAuth授权URL

**参数**:
- `userId`: 用户ID（作为state参数传递）

**响应示例**:
```json
{
  "authUrl": "https://api.notion.com/v1/oauth/authorize?client_id=xxx&redirect_uri=xxx&response_type=code&owner=user&state=user123"
}
```

#### GET /notion/callback
处理Notion OAuth回调

**参数**:
- `code`: 授权码
- `state`: 用户ID
- `error`: 错误信息（如果有）

**流程**:
1. 验证授权码
2. 交换访问令牌
3. 获取用户数据库列表
4. 显示数据库选择页面

#### POST /notion/complete-binding
完成数据库绑定

**请求体**:
```json
{
  "userId": "user123",
  "accessToken": "secret_xxx",
  "databaseId": "database_id"
}
```

#### POST /notion/databases
获取用户数据库列表

**请求体**:
```json
{
  "accessToken": "secret_xxx"
}
```

**响应示例**:
```json
[
  {
    "id": "database_id",
    "title": "我的记账数据库",
    "properties": {...}
  }
]
```

#### POST /notion/bind
创建Notion绑定（API方式）

**请求体**:
```json
{
  "userId": "user123",
  "accessToken": "secret_xxx",
  "databaseId": "database_id",
  "databaseName": "我的记账数据库"
}
```

#### GET /notion/binding/:userId
获取用户Notion绑定信息

**响应示例**:
```json
{
  "bound": true,
  "databaseId": "database_id",
  "databaseName": "我的记账数据库"
}
```

### 6. 订阅管理

#### POST /subscription
创建订阅

**请求体**:
```json
{
  "userId": "user123",
  "plan": "monthly"
}
```

#### GET /subscription/:userId/current
获取当前订阅

#### GET /subscription/:userId/status
检查订阅状态

#### POST /subscription/:userId/renew
续费订阅

#### DELETE /subscription/:userId
取消订阅

#### GET /subscription/:userId/history
获取订阅历史

### 8. 用量管理

#### GET /usage/:userId/current
获取当前使用统计

**响应示例**:
```json
{
  "userId": "user123",
  "yearMonth": 202307,
  "count": 15,
  "limit": 1000,
  "remaining": 985,
  "percentage": 1.5
}
```

#### GET /usage/:userId/check-limit
检查是否超出限额

#### GET /usage/:userId/history
获取使用历史

#### GET /usage/system/stats
获取系统统计

## 错误处理

所有API错误都遵循统一的格式：

```json
{
  "statusCode": 400,
  "timestamp": "2023-07-12T10:00:00.000Z",
  "path": "/api/endpoint",
  "method": "POST",
  "error": "Bad Request",
  "message": "具体错误信息"
}
```

## 状态码

- `200`: 成功
- `201`: 创建成功
- `400`: 请求错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `429`: 请求过于频繁
- `500`: 服务器内部错误

## 限流

API实现了基于IP的限流机制：
- 每分钟最多100次请求
- 超出限制返回429状态码

## 示例代码

### JavaScript/Node.js

```javascript
const axios = require('axios');

// 处理记账请求
async function processBilling(userId, content) {
  try {
    const response = await axios.post('http://localhost:3000/billing/process', {
      userId,
      messageContent: {
        type: 'text',
        content
      }
    });
    
    console.log('记账结果:', response.data);
  } catch (error) {
    console.error('记账失败:', error.response.data);
  }
}

// 获取用户使用统计
async function getUserUsage(userId) {
  try {
    const response = await axios.get(`http://localhost:3000/usage/${userId}/current`);
    console.log('使用统计:', response.data);
  } catch (error) {
    console.error('获取统计失败:', error.response.data);
  }
}
```

### Python

```python
import requests

def process_billing(user_id, content):
    url = 'http://localhost:3000/billing/process'
    data = {
        'userId': user_id,
        'messageContent': {
            'type': 'text',
            'content': content
        }
    }
    
    try:
        response = requests.post(url, json=data)
        response.raise_for_status()
        print('记账结果:', response.json())
    except requests.exceptions.RequestException as e:
        print('记账失败:', e)

def get_user_usage(user_id):
    url = f'http://localhost:3000/usage/{user_id}/current'
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        print('使用统计:', response.json())
    except requests.exceptions.RequestException as e:
        print('获取统计失败:', e)
```
