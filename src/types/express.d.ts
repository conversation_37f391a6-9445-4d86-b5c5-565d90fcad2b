// /src/types/express.d.ts

/**
 * 扩展 Express 的 Request 接口，用于添加自定义属性。
 * 这是为了让 TypeScript 编译器知道 req.user 的存在，
 * 该属性通常由认证中间件（如 Passport.js）在用户通过验证后附加。
 */
/**
 * 定义附加到请求对象上的用户信息的结构。
 * 这为 req.user 提供了强类型，避免了 any 类型带来的风险。
 */
interface UserPayload {
  id: string;
  // 未来可以根据需要扩展，例如添加 username, roles 等字段
}

declare namespace Express {
  export interface Request {
    /**
     * 附加到请求对象上的用户信息。
     * 使用了自定义的 UserPayload 接口来增强类型安全。
     */
    user?: UserPayload;
  }
}
