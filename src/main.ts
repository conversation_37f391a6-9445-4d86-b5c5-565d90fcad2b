import { NestFactory } from '@nestjs/core';
import { Val<PERSON><PERSON>Pipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { Request, Response, NextFunction } from 'express';
import * as express from 'express';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from './common/filters/global-exception.filter';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { RequestIdMiddleware } from './common/middleware/request-id.middleware';
import { RawBodyMiddleware } from './common/middleware/raw-body.middleware';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  const app = await NestFactory.create(AppModule, {
    logger: ['log', 'error', 'warn', 'debug', 'verbose'],
    bodyParser: false, // 禁用默认的 body parser
  });

  // 安全中间件
  // app.use(
  //   helmet({
  //     contentSecurityPolicy: {
  //       directives: {
  //         defaultSrc: ["'self'"],
  //         styleSrc: [
  //           "'self'",
  //           "'unsafe-inline'",
  //           'https://fonts.googleapis.com',
  //           'https://cdnjs.cloudflare.com',
  //         ],
  //         scriptSrc: [
  //           "'self'",
  //           "'unsafe-inline'",
  //           'https://cdnjs.cloudflare.com',
  //         ],
  //         imgSrc: ["'self'", 'data:', 'https:', 'http:'],
  //         connectSrc: [
  //           "'self'",
  //           'https://api.notion.com',

  //           'https://qyapi.weixin.qq.com',
  //           'http://localhost:*',
  //           'https://flashbk.baiwotech.com:*',
  //         ],
  //         fontSrc: ["'self'", 'https:', 'data:'],
  //         objectSrc: ["'none'"],
  //         mediaSrc: ["'self'", 'https:', 'http:'],
  //         frameSrc: ["'self'"],
  //       },
  //     },
  //     crossOriginEmbedderPolicy: false,
  //     crossOriginResourcePolicy: { policy: 'cross-origin' },
  //     crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
  //   }),
  // );

  // TODO: 配置 Winston 文件日志器
  // 暂时使用默认控制台日志，稍后配置文件日志

  // 配置自定义 body parser，为企业微信回调保留原始数据
  app.use('/wecom/callback', express.raw({ type: 'application/xml' }));
  app.use('/wecom/callback', express.raw({ type: 'text/xml' }));
  app.use('/wecom/callback', express.text({ type: 'text/plain' }));

  // 为其他路由配置标准 body parser
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // 配置RabbitMQ微服务
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [process.env.RABBITMQ_URL || 'amqp://localhost:5672'],
      queue: 'main_queue',
      queueOptions: {
        durable: true,
      },
      socketOptions: {
        heartbeatIntervalInSeconds: 60,
        reconnectTimeInSeconds: 5,
      },
    },
  });

  // 全局中间件
  const requestIdMiddleware = new RequestIdMiddleware();
  app.use((req: Request, res: Response, next: NextFunction) =>
    requestIdMiddleware.use(req, res, next),
  );

  // 原始数据中间件（用于企业微信回调）
  const rawBodyMiddleware = new RawBodyMiddleware();
  app.use((req: Request, res: Response, next: NextFunction) =>
    rawBodyMiddleware.use(req, res, next),
  );

  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  // 全局过滤器
  app.useGlobalFilters(new GlobalExceptionFilter());

  // 全局拦截器
  app.useGlobalInterceptors(new LoggingInterceptor());

  // CORS配置
  app.enableCors({
    origin: process.env.CORS_ORIGIN || true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
    allowedHeaders: 'Content-Type, Accept, Authorization, X-Requested-With',
    exposedHeaders: ['X-Total-Count'],
  });

  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('FlashBookkeeping API')
    .setDescription('闪念记账 - 智能记账应用API文档')
    .setVersion('1.0')
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // 优化的Swagger UI配置，使用CDN资源
  SwaggerModule.setup('api', app, document);

  // 启动微服务
  await app.startAllMicroservices();
  logger.log('🐰 RabbitMQ microservice started');

  const port = process.env.PORT || 3000;

  // 使用HTTPS配置时，需要SSL证书
  // 生产环境建议通过反向代理（如Nginx）处理HTTPS
  await app.listen(port, '0.0.0.0');

  logger.log(`🚀 Application is running on: http://localhot:${port}`);
  logger.log(`📚 Swagger documentation: http://localhot:${port}/api`);
}

bootstrap().catch((error) => {
  console.error('Failed to start application:', error);
  process.exit(1);
});
