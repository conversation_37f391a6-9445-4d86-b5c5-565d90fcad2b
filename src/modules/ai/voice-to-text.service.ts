import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as fs from 'fs';
import * as path from 'path';
import * as FormData from 'form-data';

export interface VoiceToTextRequest {
  audioPath: string;
  language?: string;
  format?: string;
  sampleRate?: number;
}

export interface VoiceToTextResponse {
  text: string;
  confidence: number;
  segments?: {
    start: number;
    end: number;
    text: string;
    confidence: number;
  }[];
  rawData: any;
}

export interface VoiceProvider {
  name: string;
  transcribe(request: VoiceToTextRequest): Promise<VoiceToTextResponse>;
}

// 腾讯云语音识别实现
@Injectable()
export class TencentVoiceProvider implements VoiceProvider {
  name = 'tencent';
  private readonly logger = new Logger(TencentVoiceProvider.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async transcribe(request: VoiceToTextRequest): Promise<VoiceToTextResponse> {
    const secretId = this.configService.get<string>('TENCENT_SECRET_ID');
    const secretKey = this.configService.get<string>('TENCENT_SECRET_KEY');
    
    if (!secretId || !secretKey) {
      throw new Error('腾讯云语音配置不完整');
    }

    try {
      const audioBuffer = fs.readFileSync(request.audioPath);
      const base64Audio = audioBuffer.toString('base64');

      const response = await firstValueFrom(
        this.httpService.post(
          'https://asr.tencentcloudapi.com/',
          {
            EngineModelType: '16k_zh',
            ChannelNum: 1,
            ResTextFormat: 1,
            SourceType: 1,
            Data: base64Audio,
            DataLen: audioBuffer.length,
          },
          {
            headers: {
              'Authorization': this.generateTencentAuth(),
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      return this.parseTencentResponse(response.data);
    } catch (error) {
      this.logger.error('腾讯云语音识别失败', error);
      throw new Error(`语音识别失败: ${error.message}`);
    }
  }

  private generateTencentAuth(): string {
    // 简化的认证生成，实际应使用腾讯云SDK
    const secretId = this.configService.get<string>('TENCENT_SECRET_ID');
    return `TC3-HMAC-SHA256 Credential=${secretId}/2024-01-01/asr/tc3_request`;
  }

  private parseTencentResponse(data: any): VoiceToTextResponse {
    const result = data.Response;
    
    if (result.Error) {
      throw new Error(result.Error.Message);
    }

    const text = result.Result || '';
    
    return {
      text,
      confidence: 0.9,
      segments: this.parseSegments(result.SentenceList),
      rawData: data,
    };
  }

  private parseSegments(sentences: any[]): VoiceToTextResponse['segments'] {
    if (!sentences) return undefined;
    
    return sentences.map(sentence => ({
      start: sentence.StartTime,
      end: sentence.EndTime,
      text: sentence.FinalSentence,
      confidence: sentence.WordSize > 0 ? 0.9 : 0.5,
    }));
  }
}

// 阿里云语音识别实现
@Injectable()
export class AliyunVoiceProvider implements VoiceProvider {
  name = 'aliyun';
  private readonly logger = new Logger(AliyunVoiceProvider.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async transcribe(request: VoiceToTextRequest): Promise<VoiceToTextResponse> {
    const appKey = this.configService.get<string>('ALIYUN_VOICE_APP_KEY');
    const token = this.configService.get<string>('ALIYUN_VOICE_TOKEN');
    
    if (!appKey || !token) {
      throw new Error('阿里云语音配置不完整');
    }

    try {
      const audioBuffer = fs.readFileSync(request.audioPath);
      const base64Audio = audioBuffer.toString('base64');

      const response = await firstValueFrom(
        this.httpService.post(
          'https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/asr',
          {
            format: request.format || 'wav',
            sample_rate: request.sampleRate || 16000,
            enable_punctuation_prediction: true,
            enable_inverse_text_normalization: true,
            enable_voice_detection: false,
            payload: {
              input_audio: base64Audio,
            },
          },
          {
            headers: {
              'X-NLS-Token': token,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      return this.parseAliyunResponse(response.data);
    } catch (error) {
      this.logger.error('阿里云语音识别失败', error);
      throw new Error(`语音识别失败: ${error.message}`);
    }
  }

  private parseAliyunResponse(data: any): VoiceToTextResponse {
    if (data.status !== 20000000) {
      throw new Error(`语音识别失败: ${data.status_text}`);
    }

    return {
      text: data.result,
      confidence: 0.9,
      segments: this.parseSegments(data.words),
      rawData: data,
    };
  }

  private parseSegments(words: any[]): VoiceToTextResponse['segments'] {
    if (!words) return undefined;
    
    return words.map(word => ({
      start: word.begin_time,
      end: word.end_time,
      text: word.word,
      confidence: word.confidence,
    }));
  }
}

// 百度语音识别实现
@Injectable()
export class BaiduVoiceProvider implements VoiceProvider {
  name = 'baidu';
  private readonly logger = new Logger(BaiduVoiceProvider.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async transcribe(request: VoiceToTextRequest): Promise<VoiceToTextResponse> {
    const appId = this.configService.get<string>('BAIDU_VOICE_APP_ID');
    const apiKey = this.configService.get<string>('BAIDU_VOICE_API_KEY');
    const secretKey = this.configService.get<string>('BAIDU_VOICE_SECRET_KEY');
    
    if (!appId || !apiKey || !secretKey) {
      throw new Error('百度语音配置不完整');
    }

    try {
      const accessToken = await this.getBaiduAccessToken(apiKey, secretKey);
      const audioBuffer = fs.readFileSync(request.audioPath);
      const base64Audio = audioBuffer.toString('base64');

      const form = new FormData();
      form.append('format', request.format || 'wav');
      form.append('rate', (request.sampleRate || 16000).toString());
      form.append('channel', '1');
      form.append('token', accessToken);
      form.append('cuid', 'flashbookkeeping');
      form.append('len', audioBuffer.length.toString());
      form.append('speech', base64Audio);

      const response = await firstValueFrom(
        this.httpService.post(
          'https://vop.baidu.com/server_api',
          form,
          {
            headers: form.getHeaders(),
          },
        ),
      );

      return this.parseBaiduResponse(response.data);
    } catch (error) {
      this.logger.error('百度语音识别失败', error);
      throw new Error(`语音识别失败: ${error.message}`);
    }
  }

  private async getBaiduAccessToken(apiKey: string, secretKey: string): Promise<string> {
    const response = await firstValueFrom(
      this.httpService.get(
        `https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${apiKey}&client_secret=${secretKey}`,
      ),
    );

    if (response.data.error) {
      throw new Error(`获取百度token失败: ${response.data.error_description}`);
    }

    return response.data.access_token;
  }

  private parseBaiduResponse(data: any): VoiceToTextResponse {
    if (data.err_no !== 0) {
      throw new Error(`语音识别失败: ${data.err_msg}`);
    }

    return {
      text: data.result?.[0] || '',
      confidence: 0.9,
      segments: undefined, // 百度标准API不返回分段信息
      rawData: data,
    };
  }
}

// 语音转文字服务主类
@Injectable()
export class VoiceToTextService {
  private readonly logger = new Logger(VoiceToTextService.name);
  private providers: Map<string, VoiceProvider> = new Map();

  constructor(
    private readonly tencentProvider: TencentVoiceProvider,
    private readonly aliyunProvider: AliyunVoiceProvider,
    private readonly baiduProvider: BaiduVoiceProvider,
  ) {
    this.providers.set('tencent', tencentProvider);
    this.providers.set('aliyun', aliyunProvider);
    this.providers.set('baidu', baiduProvider);
  }

  /**
   * 执行语音转文字
   */
  async transcribe(
    audioPath: string,
    provider: string = 'tencent',
    options?: {
      language?: string;
      format?: string;
      sampleRate?: number;
    },
  ): Promise<VoiceToTextResponse> {
    const voiceProvider = this.providers.get(provider);
    if (!voiceProvider) {
      throw new Error(`不支持的语音提供商: ${provider}`);
    }

    try {
      const request: VoiceToTextRequest = {
        audioPath,
        language: options?.language || 'zh-CN',
        format: options?.format,
        sampleRate: options?.sampleRate,
      };

      return await voiceProvider.transcribe(request);
    } catch (error) {
      this.logger.error(`语音转文字失败: ${provider}`, error);
      throw error;
    }
  }

  /**
   * 批量语音转文字
   */
  async batchTranscribe(
    audioPaths: string[],
    provider: string = 'tencent',
    options?: {
      language?: string;
      format?: string;
      sampleRate?: number;
    },
  ): Promise<VoiceToTextResponse[]> {
    const results: VoiceToTextResponse[] = [];
    
    for (const audioPath of audioPaths) {
      try {
        const result = await this.transcribe(audioPath, provider, options);
        results.push(result);
      } catch (error) {
        this.logger.error(`批量语音转文字失败: ${audioPath}`, error);
        results.push({
          text: '',
          confidence: 0,
          rawData: { error: error.message },
        });
      }
    }

    return results;
  }

  /**
   * 获取支持的语音提供商
   */
  getSupportedProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  /**
   * 转换音频格式（如果需要）
   */
  async convertAudioFormat(
    inputPath: string,
    outputPath: string,
    options: {
      format: string;
      sampleRate: number;
      channels: number;
    } = {
      format: 'wav',
      sampleRate: 16000,
      channels: 1,
    },
  ): Promise<void> {
    // 这里应该使用音频处理库如ffmpeg
    // 为简化，直接复制文件
    try {
      fs.copyFileSync(inputPath, outputPath);
    } catch (error) {
      this.logger.error('音频格式转换失败', error);
      throw new Error(`音频转换失败: ${error.message}`);
    }
  }

  /**
   * 验证音频文件
   */
  async validateAudioFile(filePath: string): Promise<{
    isValid: boolean;
    format?: string;
    duration?: number;
    sampleRate?: number;
    error?: string;
  }> {
    try {
      const stats = fs.statSync(filePath);
      if (stats.size === 0) {
        return { isValid: false, error: '音频文件为空' };
      }

      if (stats.size > 10 * 1024 * 1024) {
        return { isValid: false, error: '音频文件过大，最大支持10MB' };
      }

      // 这里应该检查音频格式，简化处理
      const extension = path.extname(filePath).toLowerCase();
      const supportedFormats = ['.wav', '.mp3', '.m4a', '.amr', '.aac'];
      
      if (!supportedFormats.includes(extension)) {
        return { isValid: false, error: `不支持的音频格式: ${extension}` };
      }

      return {
        isValid: true,
        format: extension.slice(1),
        duration: 0, // 简化处理
        sampleRate: 16000, // 默认采样率
      };
    } catch (error) {
      return { isValid: false, error: `文件验证失败: ${error.message}` };
    }
  }
}