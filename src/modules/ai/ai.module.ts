import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { AiService } from './ai.service';
import { AiController } from './ai.controller';
import { OCRService } from './ocr.service';
import { VoiceToTextService } from './voice-to-text.service';
import { TencentOCRProvider, AliyunOCRProvider, BaiduOCRProvider } from './ocr.service';
import { TencentVoiceProvider, AliyunVoiceProvider, BaiduVoiceProvider } from './voice-to-text.service';

@Module({
  imports: [HttpModule, ConfigModule],
  controllers: [AiController],
  providers: [
    AiService,
    OCRService,
    VoiceToTextService,
    TencentOCRProvider,
    AliyunOCRProvider,
    BaiduOCRProvider,
    TencentVoiceProvider,
    AliyunVoiceProvider,
    BaiduVoiceProvider,
  ],
  exports: [AiService, OCRService, VoiceToTextService],
})
export class AiModule {}
