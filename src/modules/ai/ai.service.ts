import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

// 定义豆包 API 返回的数据结构
interface DoubaoResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

// 定义 AI 响应的完整结构
export interface AIResponse {
  intent: string;
  bookkeeping_data: BillingInfo;
  query_params: QueryResult;
}

// 定义账单信息的接口（保持向后兼容）
export interface BillingInfo {
  error: string | null;
  type: 'income' | 'expense';
  amount: number;
  category: string;
  date: string;
  description: string;
  confidence?: number;
}

export interface QueryResult {
  error: string | null;
  start_date: string;
  end_date: string;
  category: string;
  amount: number;
}

// 定义支持的消息类型
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VOICE = 'voice',
  FILE = 'file',
}

// 定义消息内容接口
export interface MessageContent {
  type: MessageType;
  content: string;
  url?: string; // 图片、语音、文件的URL
  mimeType?: string; // 文件类型
}

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 从队列任务中提取账单信息
   */
  async extractBillingInfoFromQueue(
    messageType: string,
    content: string,
    mediaUrl?: string,
  ): Promise<BillingInfo> {
    try {
      this.logger.log('开始AI分析队列任务');

      // 构建消息内容
      const messageContent: MessageContent = {
        type: this.getMessageType(messageType),
        content,
        url: mediaUrl,
      };

      return await this.extractBillingInfo(messageContent);
    } catch (error) {
      this.logger.error('AI分析队列任务失败:', error);
      throw error;
    }
  }

  /**
   * 将字符串消息类型转换为枚举
   */
  private getMessageType(messageType: string): MessageType {
    switch (messageType) {
      case 'text':
        return MessageType.TEXT;
      case 'image':
        return MessageType.IMAGE;
      case 'voice':
        return MessageType.VOICE;
      case 'file':
        return MessageType.FILE;
      default:
        return MessageType.TEXT;
    }
  }

  /**
   * 从多模态内容中提取账单信息
   * @param content 消息内容
   * @returns 提取的账单信息
   */
  async extractBillingInfo(content: MessageContent): Promise<BillingInfo> {
    try {
      // 根据内容类型选择处理方式
      switch (content.type) {
        case MessageType.TEXT:
          return await this.processTextContent(content.content);
        case MessageType.IMAGE:
          return await this.processImageContent(content.url!);
        case MessageType.VOICE:
          return await this.processVoiceContent(content.url!);
        case MessageType.FILE:
          return await this.processFileContent(content.url!, content.mimeType);
        default:
          return this.createErrorResponse('不支持的消息类型');
      }
    } catch (error) {
      this.logger.error('提取账单信息失败:', error);
      return this.createErrorResponse('处理消息时发生错误');
    }
  }

  /**
   * 处理文本内容
   */
  private async processTextContent(text: string): Promise<BillingInfo> {
    const prompt = this.buildPrompt();
    return await this.callDoubaoAPI(prompt, text);
  }

  /**
   * 处理图片内容
   */
  private async processImageContent(imageUrl: string): Promise<BillingInfo> {
    const prompt = this.buildPrompt();
    return await this.callDoubaoAPI(prompt, '', imageUrl);
  }

  /**
   * 处理语音内容（需要先转文字）
   */
  private async processVoiceContent(voiceUrl: string): Promise<BillingInfo> {
    // TODO: 实现语音转文字功能
    this.logger.warn('语音处理功能暂未实现');
    return this.createErrorResponse('语音处理功能暂未实现');
  }

  /**
   * 处理文件内容
   */
  private async processFileContent(
    fileUrl: string,
    mimeType?: string,
  ): Promise<BillingInfo> {
    // TODO: 根据文件类型处理（PDF、Excel等）
    this.logger.warn('文件处理功能暂未实现');
    return this.createErrorResponse('文件处理功能暂未实现');
  }

  /**
   * 构建提示词
   */
  private buildPrompt(): string {
    // 获取今天的日期
    const current_date = new Date().toISOString().split('T')[0];
    const basePrompt = `
[角色 (Role)]
你是一个全能的AI财务助手，名叫“闪念记账”。你既是细心的记账员，又是敏锐的数据分析师。你的核心职责是理解用户的任何与财务相关的自然语言指令，并将其转化为结构化的数据或查询参数。你处理任务时冷静、精准，并且严格遵循输出格式。
[任务 (Task)]
你的任务是分析用户的输入（文本、图片、语音、文件），并执行以下三个步骤：
意图识别 (Intent Recognition): 首先，判断用户的核心意图。意图分为三类：
bookkeeping：用户想要记录一笔收入或支出。
query：用户想要查询或汇总自己的账目。
other：用户在闲聊、打招呼或发送无关信息。
条件执行 (Conditional Execution): 根据识别出的意图，执行相应的子任务：
如果意图是 bookkeeping: 从输入中提取记账的六个核心要素：type, amount, category, date, description。
如果意图是 query: 从输入中提取数据查询的三个关键参数：start_date, end_date, category。
如果意图是 other: 无需执行额外任务。
结构化输出 (Structured Output): 将分析结果整合到一个统一的JSON结构中返回。
[限制 (Constraints)]
绝对的JSON输出： 你的唯一输出必须是一个格式正确的JSON对象。禁止在JSON之外包含任何文本、注释或问候。
统一的输出结构： 无论何种意图，都必须使用下面[输出]部分定义的统一JSON结构。通过intent字段来区分不同任务的结果。
字段互斥性：
当 intent 是 bookkeeping 时，bookkeeping_data 字段必须被填充，而 query_params 字段必须为 null。
当 intent 是 query 时，query_params 字段必须被填充，而 bookkeeping_data 字段必须为 null。
当 intent 是 other 时，bookkeeping_data 和 query_params 两个字段都必须为 null。
记账规则 (同前)：
category 必须从预设列表选择：餐饮, 交通, 购物, 娱乐, 居家, 通讯, 学习, 医疗, 人情, 理财, 收入, 还贷, 其他。
date 默认为 ${current_date}，格式为 YYYY-MM-DD。
amount 为纯数字。
查询规则 (同前)：
能理解并转换各种口语化时间，如“本周”、“上月”。
若无特定时间或分类，对应字段设为 null。
占位符： {{current_date}} 由后端程序在请求时动态替换。
[输出 (Output)]
必须严格遵循以下统一的JSON结构：
Generated json
{
  "intent": "bookkeeping" | "query" | "other",
  "bookkeeping_data": {
    "is_bookkeeping": true,
    "type": "expense" | "income",
    "amount": number,
    "category": "string",
    "date": "YYYY-MM-DD",
    "description": "string"
  } | null,
  "query_params": {
    "start_date": "YYYY-MM-DD" | null,
    "end_date": "YYYY-MM-DD" | null,
    "category": "string" | null,
    "amount": number
  } | null
}
Use code with caution.
Json
示例演示
场景1：记账
用户输入： “刚刚打车回家，花了45块5”
模型应返回的JSON：
Generated json
{
  "intent": "bookkeeping",
  "bookkeeping_data": {
    "is_bookkeeping": true,
    "type": "expense",
    "amount": 45.5,
    "category": "交通",
    "date": "2023-10-27",
    "description": "打车回家"
  },
  "query_params": null
}
Use code with caution.
Json
场景2：查询
用户输入： “这个月我在吃饭上花了多少钱？”
模型应返回的JSON：
Generated json
{
  "intent": "query",
  "bookkeeping_data": null,
  "query_params": {
    "start_date": "2023-10-01",
    "end_date": "2023-10-31",
    "category": "餐饮",
    "amount": 900
  }
}
Use code with caution.
Json
场景3：闲聊
用户输入： “你好呀，闪念记账”
模型应返回的JSON：
Generated json
{
  "intent": "other",
  "bookkeeping_data": null,
  "query_params": null
}
Use code with caution.
Json
场景4：带图片的记账
用户输入： (发送一张星巴克小票图片)
模型应返回的JSON：
Generated json
{
  "intent": "bookkeeping",
  "bookkeeping_data": {
    "is_bookkeeping": true,
    "type": "expense",
    "amount": 32.00,
    "category": "餐饮",
    "date": "2023-10-27",
    "description": "星巴克"
  },
  "query_params": null
}
    `;

    return basePrompt;
  }

  /**
   * 调用豆包API
   */
  private async callDoubaoAPI(
    prompt: string,
    text: string,
    imageUrl?: string,
  ): Promise<BillingInfo> {
    const apiKey = this.configService.get<string>('DOUBAO_API_KEY');
    const endpoint = this.configService.get<string>('DOUBAO_API_ENDPOINT');
    const modelId = this.configService.get<string>('DOUBAO_MODEL_ID');

    if (!apiKey || !endpoint || !modelId) {
      this.logger.error('豆包 API 配置缺失');
      return this.createErrorResponse('AI 服务未正确配置');
    }

    const userContent: any[] = [];
    if (text) {
      userContent.push({ type: 'text', text });
    }
    if (imageUrl) {
      userContent.push({ type: 'image_url', image_url: { url: imageUrl } });
    }

    const requestBody = {
      model: modelId, // 使用环境变量中的模型端点ID
      messages: [
        {
          role: 'system',
          content: prompt,
        },
        {
          role: 'user',
          content: userContent,
        },
      ],
      temperature: 0.1, // 降低随机性，提高一致性
      max_tokens: 1000,
      stream: false,
    };

    try {
      const { data } = await firstValueFrom(
        this.httpService.post<DoubaoResponse>(endpoint, requestBody, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30秒超时
        }),
      );

      const responseContent = data.choices[0]?.message?.content;
      if (!responseContent) {
        return this.createErrorResponse('AI 服务返回空内容');
      }

      this.logger.log('AI响应内容:', responseContent);

      return this.parseAIResponse(responseContent);
    } catch (error) {
      this.logger.error('调用豆包 API 失败:', error);
      return this.createErrorResponse('调用大模型服务失败');
    }
  }

  /**
   * 解析AI响应
   */
  private parseAIResponse(content: string): BillingInfo {
    try {
      // 尝试提取JSON部分
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? jsonMatch[0] : content;

      // 首先尝试解析为新的 AI 响应格式
      let parsed: any;
      try {
        parsed = JSON.parse(jsonStr);
      } catch (parseError) {
        this.logger.error('JSON解析失败:', parseError);
        return this.createErrorResponse('无法解析AI服务返回的数据');
      }

      // 检查是否为新的 AI 响应格式
      if (parsed.intent && parsed.bookkeeping_data) {
        const aiResponse = parsed as AIResponse;

        // 验证是否为记账相关的意图
        if (aiResponse.intent !== 'bookkeeping') {
          return this.createErrorResponse('该内容不是记账相关信息');
        }

        const billingInfo: BillingInfo = aiResponse.bookkeeping_data;

        // 验证必要字段
        if (typeof billingInfo.amount !== 'number' || billingInfo.amount < 0) {
          return this.createErrorResponse('金额格式不正确');
        }

        if (!billingInfo.category || !billingInfo.description) {
          return this.createErrorResponse('缺少必要的账单信息');
        }
        return billingInfo;
      }
      return this.createErrorResponse('无法解析AI服务返回的数据');
    } catch (error) {
      this.logger.error('解析AI响应失败:', error);
      this.logger.debug('原始响应内容:', content);
      return this.createErrorResponse('无法解析AI服务返回的数据');
    }
  }

  /**
   * 创建错误响应
   */
  private createErrorResponse(errorMessage: string): BillingInfo {
    return {
      error: errorMessage,
      amount: 0,
      category: '其他',
      date: new Date().toISOString().split('T')[0],
      description: '处理失败',
      type: 'expense',
    };
  }

  /**
   * 验证账单信息
   */
  async validateBillingInfo(billingInfo: BillingInfo): Promise<boolean> {
    let result = true;
    if (billingInfo.error) {
      result = false;
    }

    if (billingInfo.amount <= 0) {
      result = false;
    }

    const validCategories = [
      '餐饮',
      '交通',
      '购物',
      '娱乐',
      '居家',
      '通讯',
      '学习',
      '医疗',
      '人情',
      '理财',
      '收入',
      '还贷',
      '其他',
    ];

    if (!validCategories.includes(billingInfo.category)) {
      result = false;
    }

    // 验证日期格式
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(billingInfo.date)) {
      result = false;
    }

    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(result);
      }, 1000);
    }).then(() => result);
  }

  /**
   * 从文本中提取记账信息（简化版，用于媒体处理流程）
   */
  async extractBillingInfoFromText(text: string): Promise<BillingInfo> {
    if (!text || text.trim() === '') {
      return this.createErrorResponse('文本内容为空');
    }

    const messageContent: MessageContent = {
      type: MessageType.TEXT,
      content: text,
    };

    return await this.extractBillingInfo(messageContent);
  }
}
