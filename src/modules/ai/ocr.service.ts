import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as fs from 'fs';
import * as FormData from 'form-data';

export interface OCRRequest {
  imagePath: string;
  language?: string;
  extractFields?: string[];
}

export interface OCRResponse {
  text: string;
  confidence: number;
  extractedFields: {
    amount?: string;
    date?: string;
    vendor?: string;
    category?: string;
    invoiceNumber?: string;
    taxNumber?: string;
  };
  rawData: any;
}

export interface OCRProvider {
  name: string;
  recognize(request: OCRRequest): Promise<OCRResponse>;
}

// 腾讯云OCR实现
@Injectable()
export class TencentOCRProvider implements OCRProvider {
  name = 'tencent';
  private readonly logger = new Logger(TencentOCRProvider.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async recognize(request: OCRRequest): Promise<OCRResponse> {
    const secretId = this.configService.get<string>('TENCENT_SECRET_ID');
    const secretKey = this.configService.get<string>('TENCENT_SECRET_KEY');
    
    if (!secretId || !secretKey) {
      throw new Error('腾讯云OCR配置不完整');
    }

    try {
      // 这里应该使用腾讯云SDK，为简化使用HTTP调用
      const form = new FormData();
      form.append('image', fs.createReadStream(request.imagePath));
      
      const response = await firstValueFrom(
        this.httpService.post(
          'https://ocr.tencentcloudapi.com/',
          form,
          {
            headers: {
              ...form.getHeaders(),
              'Authorization': this.generateTencentAuth(),
            },
          },
        ),
      );

      return this.parseTencentResponse(response.data);
    } catch (error) {
      this.logger.error('腾讯云OCR识别失败', error);
      throw new Error(`OCR识别失败: ${error.message}`);
    }
  }

  private generateTencentAuth(): string {
    // 简化的认证生成，实际应使用腾讯云SDK
    const secretId = this.configService.get<string>('TENCENT_SECRET_ID');
    const secretKey = this.configService.get<string>('TENCENT_SECRET_KEY');
    return `TC3-HMAC-SHA256 Credential=${secretId}/2024-01-01/ocr/tc3_request, SignedHeaders=content-type;host, Signature=xxx`;
  }

  private parseTencentResponse(data: any): OCRResponse {
    // 解析腾讯云OCR响应
    const text = data.TextDetections?.map((t: any) => t.DetectedText).join('\n') || '';
    
    return {
      text,
      confidence: data.TextDetections?.[0]?.Confidence || 0.8,
      extractedFields: this.extractFieldsFromText(text),
      rawData: data,
    };
  }

  private extractFieldsFromText(text: string) {
    return {
      amount: this.extractAmount(text),
      date: this.extractDate(text),
      vendor: this.extractVendor(text),
      category: this.extractCategory(text),
      invoiceNumber: this.extractInvoiceNumber(text),
      taxNumber: this.extractTaxNumber(text),
    };
  }

  private extractAmount(text: string): string | undefined {
    const patterns = [
      /￥\s*(\d+(?:\.\d{1,2})?)/,
      /¥\s*(\d+(?:\.\d{1,2})?)/,
      /(\d+(?:\.\d{1,2})?)\s*元/,
      /合计[：:]\s*(\d+(?:\.\d{1,2})?)/,
      /金额[：:]\s*(\d+(?:\.\d{1,2})?)/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }

  private extractDate(text: string): string | undefined {
    const patterns = [
      /(\d{4}-\d{1,2}-\d{1,2})/,
      /(\d{4}\/\d{1,2}\/\d{1,2})/,
      /(\d{1,2}\/\d{1,2}\/\d{4})/,
      /(\d{4}年\d{1,2}月\d{1,2}日)/,
      /(\d{1,2}月\d{1,2}日)/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }

  private extractVendor(text: string): string | undefined {
    // 常见商户名称
    const vendorKeywords = [
      '星巴克', '麦当劳', '肯德基', '必胜客', '沃尔玛', '家乐福',
      '华润万家', '永辉超市', '盒马鲜生', '叮咚买菜', '美团外卖',
      '饿了么', '滴滴出行', '高德打车', '携程', '去哪儿', '飞猪',
      '支付宝', '微信支付', '京东', '淘宝', '天猫', '拼多多',
    ];

    for (const keyword of vendorKeywords) {
      if (text.includes(keyword)) {
        return keyword;
      }
    }

    // 提取可能的商户名称
    const vendorMatch = text.match(/[\u4e00-\u9fa5]{2,}有限公司|[\u4e00-\u9fa5]{2,}店|[\u4e00-\u9fa5]{2,}超市/);
    return vendorMatch?.[0];
  }

  private extractCategory(text: string): string | undefined {
    const categoryKeywords = {
      '餐饮': ['餐厅', '饭店', '快餐', '咖啡', '奶茶', '小吃'],
      '交通': ['出租车', '地铁', '公交', '滴滴', '打车', '加油'],
      '购物': ['超市', '商场', '便利店', '购物', '零售'],
      '娱乐': ['电影', '游戏', 'KTV', '酒吧', '健身房'],
      '住宿': ['酒店', '宾馆', '民宿', '住宿'],
      '医疗': ['医院', '药店', '诊所', '医疗'],
    };

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      for (const keyword of keywords) {
        if (text.includes(keyword)) {
          return category;
        }
      }
    }

    return undefined;
  }

  private extractInvoiceNumber(text: string): string | undefined {
    const patterns = [
      /发票号码[：:]\s*([A-Z0-9-]+)/,
      /票号[：:]\s*([A-Z0-9-]+)/,
      /No\.\s*([A-Z0-9-]+)/,
      /编号[：:]\s*([A-Z0-9-]+)/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }

  private extractTaxNumber(text: string): string | undefined {
    // 中国税号格式：15位、17位、18位或20位
    const patterns = [
      /税号[：:]\s*(\d{15}|\d{17}|\d{18}|\d{20})/,
      /[\u4e00-\u9fa5]{2,}税号\s*(\d{15}|\d{17}|\d{18}|\d{20})/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }
}

// 阿里云OCR实现
@Injectable()
export class AliyunOCRProvider implements OCRProvider {
  name = 'aliyun';
  private readonly logger = new Logger(AliyunOCRProvider.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async recognize(request: OCRRequest): Promise<OCRResponse> {
    const appCode = this.configService.get<string>('ALIYUN_OCR_APP_CODE');
    
    if (!appCode) {
      throw new Error('阿里云OCR配置不完整');
    }

    try {
      const imageBuffer = fs.readFileSync(request.imagePath);
      const base64Image = imageBuffer.toString('base64');

      const response = await firstValueFrom(
        this.httpService.post(
          'https://tysbgpu.market.alicloudapi.com/api/predict/ocr_general',
          { image: base64Image },
          {
            headers: {
              'Authorization': `APPCODE ${appCode}`,
              'Content-Type': 'application/json; charset=UTF-8',
            },
          },
        ),
      );

      return this.parseAliyunResponse(response.data);
    } catch (error) {
      this.logger.error('阿里云OCR识别失败', error);
      throw new Error(`OCR识别失败: ${error.message}`);
    }
  }

  private parseAliyunResponse(data: any): OCRResponse {
    const text = data.prism_wordsInfo?.map((w: any) => w.word).join('\n') || '';
    
    return {
      text,
      confidence: data.prism_wordsInfo?.[0]?.confidence || 0.8,
      extractedFields: this.extractFieldsFromText(text),
      rawData: data,
    };
  }

  private extractFieldsFromText(text: string) {
    // 与腾讯云相同的字段提取逻辑
    return {
      amount: this.extractAmount(text),
      date: this.extractDate(text),
      vendor: this.extractVendor(text),
      category: this.extractCategory(text),
      invoiceNumber: this.extractInvoiceNumber(text),
      taxNumber: this.extractTaxNumber(text),
    };
  }

  // 复用相同的提取方法
  private extractAmount(text: string): string | undefined {
    const patterns = [
      /￥\s*(\d+(?:\.\d{1,2})?)/,
      /¥\s*(\d+(?:\.\d{1,2})?)/,
      /(\d+(?:\.\d{1,2})?)\s*元/,
      /合计[：:]\s*(\d+(?:\.\d{1,2})?)/,
      /金额[：:]\s*(\d+(?:\.\d{1,2})?)/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }

  private extractDate(text: string): string | undefined {
    const patterns = [
      /(\d{4}-\d{1,2}-\d{1,2})/,
      /(\d{4}\/\d{1,2}\/\d{1,2})/,
      /(\d{1,2}\/\d{1,2}\/\d{4})/,
      /(\d{4}年\d{1,2}月\d{1,2}日)/,
      /(\d{1,2}月\d{1,2}日)/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }

  private extractVendor(text: string): string | undefined {
    const vendorKeywords = [
      '星巴克', '麦当劳', '肯德基', '必胜客', '沃尔玛', '家乐福',
      '华润万家', '永辉超市', '盒马鲜生', '叮咚买菜', '美团外卖',
      '饿了么', '滴滴出行', '高德打车', '携程', '去哪儿', '飞猪',
      '支付宝', '微信支付', '京东', '淘宝', '天猫', '拼多多',
    ];

    for (const keyword of vendorKeywords) {
      if (text.includes(keyword)) {
        return keyword;
      }
    }

    const vendorMatch = text.match(/[\u4e00-\u9fa5]{2,}有限公司|[\u4e00-\u9fa5]{2,}店|[\u4e00-\u9fa5]{2,}超市/);
    return vendorMatch?.[0];
  }

  private extractCategory(text: string): string | undefined {
    const categoryKeywords = {
      '餐饮': ['餐厅', '饭店', '快餐', '咖啡', '奶茶', '小吃'],
      '交通': ['出租车', '地铁', '公交', '滴滴', '打车', '加油'],
      '购物': ['超市', '商场', '便利店', '购物', '零售'],
      '娱乐': ['电影', '游戏', 'KTV', '酒吧', '健身房'],
      '住宿': ['酒店', '宾馆', '民宿', '住宿'],
      '医疗': ['医院', '药店', '诊所', '医疗'],
    };

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      for (const keyword of keywords) {
        if (text.includes(keyword)) {
          return category;
        }
      }
    }

    return undefined;
  }

  private extractInvoiceNumber(text: string): string | undefined {
    const patterns = [
      /发票号码[：:]\s*([A-Z0-9-]+)/,
      /票号[：:]\s*([A-Z0-9-]+)/,
      /No\.\s*([A-Z0-9-]+)/,
      /编号[：:]\s*([A-Z0-9-]+)/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }

  private extractTaxNumber(text: string): string | undefined {
    const patterns = [
      /税号[：:]\s*(\d{15}|\d{17}|\d{18}|\d{20})/,
      /[\u4e00-\u9fa5]{2,}税号\s*(\d{15}|\d{17}|\d{18}|\d{20})/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }
}

// 百度OCR实现
@Injectable()
export class BaiduOCRProvider implements OCRProvider {
  name = 'baidu';
  private readonly logger = new Logger(BaiduOCRProvider.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async recognize(request: OCRRequest): Promise<OCRResponse> {
    const apiKey = this.configService.get<string>('BAIDU_OCR_API_KEY');
    const secretKey = this.configService.get<string>('BAIDU_OCR_SECRET_KEY');
    
    if (!apiKey || !secretKey) {
      throw new Error('百度OCR配置不完整');
    }

    try {
      const accessToken = await this.getBaiduAccessToken(apiKey, secretKey);
      const imageBuffer = fs.readFileSync(request.imagePath);
      const base64Image = imageBuffer.toString('base64');

      const response = await firstValueFrom(
        this.httpService.post(
          `https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic?access_token=${accessToken}`,
          {
            image: base64Image,
            language_type: request.language || 'CHN_ENG',
          },
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        ),
      );

      return this.parseBaiduResponse(response.data);
    } catch (error) {
      this.logger.error('百度OCR识别失败', error);
      throw new Error(`OCR识别失败: ${error.message}`);
    }
  }

  private async getBaiduAccessToken(apiKey: string, secretKey: string): Promise<string> {
    const response = await firstValueFrom(
      this.httpService.get(
        `https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${apiKey}&client_secret=${secretKey}`,
      ),
    );

    if (response.data.error) {
      throw new Error(`获取百度token失败: ${response.data.error_description}`);
    }

    return response.data.access_token;
  }

  private parseBaiduResponse(data: any): OCRResponse {
    const text = data.words_result?.map((w: any) => w.words).join('\n') || '';
    
    return {
      text,
      confidence: data.words_result?.[0]?.probability?.average || 0.8,
      extractedFields: this.extractFieldsFromText(text),
      rawData: data,
    };
  }

  private extractFieldsFromText(text: string) {
    // 复用相同的字段提取逻辑
    return {
      amount: this.extractAmount(text),
      date: this.extractDate(text),
      vendor: this.extractVendor(text),
      category: this.extractCategory(text),
      invoiceNumber: this.extractInvoiceNumber(text),
      taxNumber: this.extractTaxNumber(text),
    };
  }

  private extractAmount(text: string): string | undefined {
    const patterns = [
      /￥\s*(\d+(?:\.\d{1,2})?)/,
      /¥\s*(\d+(?:\.\d{1,2})?)/,
      /(\d+(?:\.\d{1,2})?)\s*元/,
      /合计[：:]\s*(\d+(?:\.\d{1,2})?)/,
      /金额[：:]\s*(\d+(?:\.\d{1,2})?)/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }

  private extractDate(text: string): string | undefined {
    const patterns = [
      /(\d{4}-\d{1,2}-\d{1,2})/,
      /(\d{4}\/\d{1,2}\/\d{1,2})/,
      /(\d{1,2}\/\d{1,2}\/\d{4})/,
      /(\d{4}年\d{1,2}月\d{1,2}日)/,
      /(\d{1,2}月\d{1,2}日)/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }

  private extractVendor(text: string): string | undefined {
    const vendorKeywords = [
      '星巴克', '麦当劳', '肯德基', '必胜客', '沃尔玛', '家乐福',
      '华润万家', '永辉超市', '盒马鲜生', '叮咚买菜', '美团外卖',
      '饿了么', '滴滴出行', '高德打车', '携程', '去哪儿', '飞猪',
      '支付宝', '微信支付', '京东', '淘宝', '天猫', '拼多多',
    ];

    for (const keyword of vendorKeywords) {
      if (text.includes(keyword)) {
        return keyword;
      }
    }

    const vendorMatch = text.match(/[\u4e00-\u9fa5]{2,}有限公司|[\u4e00-\u9fa5]{2,}店|[\u4e00-\u9fa5]{2,}超市/);
    return vendorMatch?.[0];
  }

  private extractCategory(text: string): string | undefined {
    const categoryKeywords = {
      '餐饮': ['餐厅', '饭店', '快餐', '咖啡', '奶茶', '小吃'],
      '交通': ['出租车', '地铁', '公交', '滴滴', '打车', '加油'],
      '购物': ['超市', '商场', '便利店', '购物', '零售'],
      '娱乐': ['电影', '游戏', 'KTV', '酒吧', '健身房'],
      '住宿': ['酒店', '宾馆', '民宿', '住宿'],
      '医疗': ['医院', '药店', '诊所', '医疗'],
    };

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      for (const keyword of keywords) {
        if (text.includes(keyword)) {
          return category;
        }
      }
    }

    return undefined;
  }

  private extractInvoiceNumber(text: string): string | undefined {
    const patterns = [
      /发票号码[：:]\s*([A-Z0-9-]+)/,
      /票号[：:]\s*([A-Z0-9-]+)/,
      /No\.\s*([A-Z0-9-]+)/,
      /编号[：:]\s*([A-Z0-9-]+)/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }

  private extractTaxNumber(text: string): string | undefined {
    const patterns = [
      /税号[：:]\s*(\d{15}|\d{17}|\d{18}|\d{20})/,
      /[\u4e00-\u9fa5]{2,}税号\s*(\d{15}|\d{17}|\d{18}|\d{20})/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return undefined;
  }
}

// OCR服务主类
@Injectable()
export class OCRService {
  private readonly logger = new Logger(OCRService.name);
  private providers: Map<string, OCRProvider> = new Map();

  constructor(
    private readonly tencentProvider: TencentOCRProvider,
    private readonly aliyunProvider: AliyunOCRProvider,
    private readonly baiduProvider: BaiduOCRProvider,
  ) {
    this.providers.set('tencent', tencentProvider);
    this.providers.set('aliyun', aliyunProvider);
    this.providers.set('baidu', baiduProvider);
  }

  /**
   * 执行OCR识别
   */
  async recognize(
    imagePath: string,
    provider: string = 'tencent',
    options?: { language?: string; extractFields?: string[] },
  ): Promise<OCRResponse> {
    const ocrProvider = this.providers.get(provider);
    if (!ocrProvider) {
      throw new Error(`不支持的OCR提供商: ${provider}`);
    }

    try {
      const request: OCRRequest = {
        imagePath,
        language: options?.language || 'zh-CN',
        extractFields: options?.extractFields,
      };

      return await ocrProvider.recognize(request);
    } catch (error) {
      this.logger.error(`OCR识别失败: ${provider}`, error);
      throw error;
    }
  }

  /**
   * 批量OCR识别
   */
  async batchRecognize(
    imagePaths: string[],
    provider: string = 'tencent',
    options?: { language?: string; extractFields?: string[] },
  ): Promise<OCRResponse[]> {
    const results: OCRResponse[] = [];
    
    for (const imagePath of imagePaths) {
      try {
        const result = await this.recognize(imagePath, provider, options);
        results.push(result);
      } catch (error) {
        this.logger.error(`批量OCR失败: ${imagePath}`, error);
        results.push({
          text: '',
          confidence: 0,
          extractedFields: {},
          rawData: { error: error.message },
        });
      }
    }

    return results;
  }

  /**
   * 获取支持的OCR提供商
   */
  getSupportedProviders(): string[] {
    return Array.from(this.providers.keys());
  }
}