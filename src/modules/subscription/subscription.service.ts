import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';

export interface CreateSubscriptionDto {
  userId: string;
  plan: 'monthly' | 'yearly';
  paymentMethod?: string;
}

export interface SubscriptionInfo {
  id: string;
  plan: string;
  limit: number;
  startDate: Date;
  endDate: Date;
  status: string;
  isActive: boolean;
}

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 创建订阅
   */
  async createSubscription(
    dto: CreateSubscriptionDto,
  ): Promise<SubscriptionInfo> {
    const { userId, plan } = dto;

    // 计算订阅期限和限额
    const now = new Date();
    const endDate = new Date(now);
    let limit = 1000; // 默认限额

    if (plan === 'monthly') {
      endDate.setMonth(endDate.getMonth() + 1);
      limit = 1000;
    } else if (plan === 'yearly') {
      endDate.setFullYear(endDate.getFullYear() + 1);
      limit = 12000;
    }

    // 取消现有的活跃订阅
    await this.prisma.subscription.updateMany({
      where: {
        user_id: userId,
        status: 'active',
      },
      data: {
        status: 'canceled',
      },
    });

    // 创建新订阅
    const subscription = await this.prisma.subscription.create({
      data: {
        user_id: userId,
        plan,
        limit,
        start_date: now,
        end_date: endDate,
        status: 'active',
      },
    });

    return {
      id: subscription.id,
      plan: subscription.plan,
      limit: subscription.limit,
      startDate: subscription.start_date,
      endDate: subscription.end_date,
      status: subscription.status,
      isActive: true,
    };
  }

  /**
   * 获取用户当前订阅
   */
  async getCurrentSubscription(
    userId: string,
  ): Promise<SubscriptionInfo | null> {
    const subscription = await this.prisma.subscription.findFirst({
      where: {
        user_id: userId,
        status: 'active',
        end_date: { gte: new Date() },
      },
      orderBy: { end_date: 'desc' },
    });

    if (!subscription) {
      return null;
    }

    return {
      id: subscription.id,
      plan: subscription.plan,
      limit: subscription.limit,
      startDate: subscription.start_date,
      endDate: subscription.end_date,
      status: subscription.status,
      isActive: new Date() <= subscription.end_date,
    };
  }

  /**
   * 检查用户是否有有效订阅
   */
  async hasValidSubscription(userId: string): Promise<boolean> {
    const subscription = await this.getCurrentSubscription(userId);
    return subscription !== null && subscription.isActive;
  }

  /**
   * 获取用户使用限额
   */
  async getUserLimit(userId: string): Promise<number> {
    const subscription = await this.getCurrentSubscription(userId);

    if (subscription) {
      return subscription.limit;
    }

    // 返回免费用户限额
    return this.configService.get<number>('FREE_USER_MONTHLY_LIMIT', 30);
  }

  /**
   * 取消订阅
   */
  async cancelSubscription(userId: string): Promise<void> {
    await this.prisma.subscription.updateMany({
      where: {
        user_id: userId,
        status: 'active',
      },
      data: {
        status: 'canceled',
      },
    });
  }

  /**
   * 续费订阅
   */
  async renewSubscription(
    userId: string,
    plan: 'monthly' | 'yearly',
  ): Promise<SubscriptionInfo> {
    const currentSubscription = await this.getCurrentSubscription(userId);

    if (!currentSubscription) {
      // 如果没有当前订阅，创建新的
      return await this.createSubscription({ userId, plan });
    }

    // 从当前订阅结束时间开始续费
    const startDate = currentSubscription.endDate;
    const endDate = new Date(startDate);
    let limit = 1000;

    if (plan === 'monthly') {
      endDate.setMonth(endDate.getMonth() + 1);
      limit = 1000;
    } else if (plan === 'yearly') {
      endDate.setFullYear(endDate.getFullYear() + 1);
      limit = 12000;
    }

    const subscription = await this.prisma.subscription.create({
      data: {
        user_id: userId,
        plan,
        limit,
        start_date: startDate,
        end_date: endDate,
        status: 'active',
      },
    });

    return {
      id: subscription.id,
      plan: subscription.plan,
      limit: subscription.limit,
      startDate: subscription.start_date,
      endDate: subscription.end_date,
      status: subscription.status,
      isActive: true,
    };
  }

  /**
   * 获取订阅历史
   */
  async getSubscriptionHistory(userId: string) {
    return await this.prisma.subscription.findMany({
      where: { user_id: userId },
      orderBy: { created_at: 'desc' },
    });
  }
}
