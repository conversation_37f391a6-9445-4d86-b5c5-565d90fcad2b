import { Controller, Get, Post, Body, Param, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  SubscriptionService,
  CreateSubscriptionDto,
} from './subscription.service';

@ApiTags('订阅管理')
@Controller('subscription')
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @ApiOperation({
    summary: '创建订阅',
    description: '为用户创建新的订阅计划',
  })
  @Post()
  async createSubscription(@Body() dto: CreateSubscriptionDto) {
    return await this.subscriptionService.createSubscription(dto);
  }

  @ApiOperation({
    summary: '获取当前订阅',
    description: '获取用户当前的活跃订阅信息',
  })
  @Get(':userId/current')
  async getCurrentSubscription(@Param('userId') userId: string) {
    const subscription =
      await this.subscriptionService.getCurrentSubscription(userId);
    if (!subscription) {
      return { hasSubscription: false };
    }
    return { hasSubscription: true, subscription };
  }

  @ApiOperation({
    summary: '检查订阅状态',
    description: '检查用户是否有有效的订阅',
  })
  @Get(':userId/status')
  async checkSubscriptionStatus(@Param('userId') userId: string) {
    const hasValid =
      await this.subscriptionService.hasValidSubscription(userId);
    const limit = await this.subscriptionService.getUserLimit(userId);

    return {
      hasValidSubscription: hasValid,
      limit,
    };
  }

  @ApiOperation({
    summary: '续费订阅',
    description: '为用户续费订阅',
  })
  @Post(':userId/renew')
  async renewSubscription(
    @Param('userId') userId: string,
    @Body() body: { plan: 'monthly' | 'yearly' },
  ) {
    return await this.subscriptionService.renewSubscription(userId, body.plan);
  }

  @ApiOperation({
    summary: '取消订阅',
    description: '取消用户的当前订阅',
  })
  @Delete(':userId')
  async cancelSubscription(@Param('userId') userId: string) {
    await this.subscriptionService.cancelSubscription(userId);
    return { success: true };
  }

  @ApiOperation({
    summary: '获取订阅历史',
    description: '获取用户的订阅历史记录',
  })
  @Get(':userId/history')
  async getSubscriptionHistory(@Param('userId') userId: string) {
    return await this.subscriptionService.getSubscriptionHistory(userId);
  }
}
