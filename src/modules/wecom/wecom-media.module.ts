import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { WecomMediaService } from './wecom-media.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [HttpModule, PrismaModule, ConfigModule],
  providers: [WecomMediaService],
  exports: [WecomMediaService],
})
export class WecomMediaModule {}