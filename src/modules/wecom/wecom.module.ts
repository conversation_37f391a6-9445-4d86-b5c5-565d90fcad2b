import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { WecomController } from './wecom.controller';
import { WecomService } from './wecom.service';
import { WecomMediaService } from './wecom-media.service';
import { UserModule } from '../user/user.module';
import { PrismaModule } from '../../prisma/prisma.module';
import { NotionModule } from '../notion/notion.module';
import { FeishuModule } from '../feishu/feishu.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    HttpModule,
    UserModule,
    PrismaModule,
    NotionModule,
    FeishuModule,
    forwardRef(() =>
      import('../queue/queue.module').then((m) => m.QueueModule),
    ),
  ],
  controllers: [WecomController],
  providers: [WecomService, WecomMediaService],
  exports: [WecomService, WecomMediaService],
})
export class WecomModule {}
