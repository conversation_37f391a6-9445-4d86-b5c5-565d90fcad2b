import { Test, TestingModule } from '@nestjs/testing';
import { WecomService } from '../wecom.service';
import { PrismaService } from '../../../prisma/prisma.service';
import { UserService } from '../../user/user.service';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';

describe('WecomService - Cursor Management', () => {
  let service: WecomService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    wecomSyncCursor: {
      findUnique: jest.fn(),
      upsert: jest.fn(),
    },
  };

  const mockUserService = {
    findOne: jest.fn(),
    create: jest.fn(),
  };

  const mockHttpService = {
    post: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockQueueService = {
    addJob: jest.fn(),
    getStats: jest.fn(),
    cleanFailed: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WecomService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: QueueService,
          useValue: mockQueueService,
        },
      ],
    }).compile();

    service = module.get<WecomService>(WecomService);
    prismaService = module.get<PrismaService>(PrismaService);

    // 设置默认的配置值
    mockConfigService.get.mockImplementation((key: string) => {
      const config = {
        WECOM_TOKEN: 'test-token',
        WECOM_ENCODING_AES_KEY: 'test-key',
        WECOM_CORP_ID: 'test-corp-id',
        WECOM_SECRET: 'test-secret',
      };
      return config[key];
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getCursor', () => {
    it('应该从数据库返回存在的cursor', async () => {
      const openKfId = 'test-kf-id';
      const expectedCursor = 'test-cursor-123';

      mockPrismaService.wecomSyncCursor.findUnique.mockResolvedValue({
        id: openKfId,
        cursor: expectedCursor,
        updated_at: new Date(),
      });

      // 使用反射访问私有方法
      const getCursor = service['getCursor'].bind(service);
      const result = await getCursor(openKfId);

      expect(result).toBe(expectedCursor);
      expect(mockPrismaService.wecomSyncCursor.findUnique).toHaveBeenCalledWith(
        {
          where: { id: openKfId },
        },
      );
    });

    it('应该在cursor不存在时返回空字符串', async () => {
      const openKfId = 'test-kf-id';

      mockPrismaService.wecomSyncCursor.findUnique.mockResolvedValue(null);

      const getCursor = service['getCursor'].bind(service);
      const result = await getCursor(openKfId);

      expect(result).toBe('');
      expect(mockPrismaService.wecomSyncCursor.findUnique).toHaveBeenCalledWith(
        {
          where: { id: openKfId },
        },
      );
    });

    it('应该在数据库错误时返回空字符串', async () => {
      const openKfId = 'test-kf-id';

      mockPrismaService.wecomSyncCursor.findUnique.mockRejectedValue(
        new Error('Database error'),
      );

      const getCursor = service['getCursor'].bind(service);
      const result = await getCursor(openKfId);

      expect(result).toBe('');
    });
  });

  describe('updateCursor', () => {
    it('应该成功更新cursor到数据库', async () => {
      const openKfId = 'test-kf-id';
      const newCursor = 'new-cursor-456';

      mockPrismaService.wecomSyncCursor.upsert.mockResolvedValue({
        id: openKfId,
        cursor: newCursor,
        updated_at: new Date(),
      });

      const updateCursor = service['updateCursor'].bind(service);
      await updateCursor(openKfId, newCursor);

      expect(mockPrismaService.wecomSyncCursor.upsert).toHaveBeenCalledWith({
        where: { id: openKfId },
        update: {
          cursor: newCursor,
          updated_at: expect.any(Date),
        },
        create: {
          id: openKfId,
          cursor: newCursor,
          updated_at: expect.any(Date),
        },
      });
    });

    it('应该在数据库错误时抛出异常', async () => {
      const openKfId = 'test-kf-id';
      const newCursor = 'new-cursor-456';

      mockPrismaService.wecomSyncCursor.upsert.mockRejectedValue(
        new Error('Database error'),
      );

      const updateCursor = service['updateCursor'].bind(service);

      await expect(updateCursor(openKfId, newCursor)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('cursor流程集成测试', () => {
    it('应该正确处理cursor的完整流程', async () => {
      const openKfId = 'test-kf-id';
      const initialCursor = '';
      const nextCursor = 'cursor-after-first-pull';

      // 模拟首次拉取（没有cursor）
      mockPrismaService.wecomSyncCursor.findUnique.mockResolvedValueOnce(null);

      // 模拟更新cursor成功
      mockPrismaService.wecomSyncCursor.upsert.mockResolvedValue({
        id: openKfId,
        cursor: nextCursor,
        updated_at: new Date(),
      });

      const getCursor = service['getCursor'].bind(service);
      const updateCursor = service['updateCursor'].bind(service);

      // 1. 首次获取cursor（应该返回空字符串）
      const firstCursor = await getCursor(openKfId);
      expect(firstCursor).toBe('');

      // 2. 更新cursor
      await updateCursor(openKfId, nextCursor);

      // 验证upsert被正确调用
      expect(mockPrismaService.wecomSyncCursor.upsert).toHaveBeenCalledWith({
        where: { id: openKfId },
        update: {
          cursor: nextCursor,
          updated_at: expect.any(Date),
        },
        create: {
          id: openKfId,
          cursor: nextCursor,
          updated_at: expect.any(Date),
        },
      });
    });
  });
});
