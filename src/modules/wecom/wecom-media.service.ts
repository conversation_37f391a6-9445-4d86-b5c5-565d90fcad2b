import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as fs from 'fs';
import * as path from 'path';
import * as mime from 'mime-types';
import { PrismaService } from '../../prisma/prisma.service';

export interface MediaDownloadResult {
  filePath: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  mediaId: string;
  originalUrl?: string;
}

export interface OCRResult {
  text: string;
  confidence: number;
  extractedAmount?: string;
  extractedDate?: string;
  vendor?: string;
}

@Injectable()
export class WecomMediaService {
  private readonly logger = new Logger(WecomMediaService.name);
  private readonly mediaDir: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly prisma: PrismaService,
  ) {
    this.mediaDir = path.join(process.cwd(), 'uploads', 'wecom-media');
    this.ensureMediaDir();
  }

  private ensureMediaDir() {
    if (!fs.existsSync(this.mediaDir)) {
      fs.mkdirSync(this.mediaDir, { recursive: true });
    }
  }

  /**
   * 下载企业微信媒体文件
   * @param mediaId 媒体文件ID
   * @param corpId 企业ID
   * @param corpSecret 企业密钥
   * @returns 下载结果
   */
  async downloadMedia(
    mediaId: string,
    corpId?: string,
    corpSecret?: string,
  ): Promise<MediaDownloadResult> {
    try {
      const accessToken = await this.getAccessToken(corpId, corpSecret);
      const mediaInfo = await this.getMediaInfo(accessToken, mediaId);
      
      const fileName = this.generateFileName(mediaId, mediaInfo.fileName);
      const filePath = path.join(this.mediaDir, fileName);
      
      await this.downloadFile(
        `https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token=${accessToken}&media_id=${mediaId}`,
        filePath,
      );

      const stats = fs.statSync(filePath);
      const mimeType = mime.lookup(filePath) || 'application/octet-stream';

      return {
        filePath,
        fileName,
        fileSize: stats.size,
        mimeType,
        mediaId,
      };
    } catch (error) {
      this.logger.error(`下载媒体文件失败: ${mediaId}`, error);
      throw new Error(`媒体下载失败: ${error.message}`);
    }
  }

  /**
   * 获取企业微信访问令牌
   */
  private async getAccessToken(
    corpId?: string,
    corpSecret?: string,
  ): Promise<string> {
    const cid = corpId || this.configService.get<string>('WECOM_CORP_ID');
    const secret = corpSecret || this.configService.get<string>('WECOM_SECRET');

    if (!cid || !secret) {
      throw new Error('企业微信配置不完整');
    }

    try {
      const response = await firstValueFrom(
        this.httpService.get(
          `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${cid}&corpsecret=${secret}`,
        ),
      );

      if (response.data.errcode !== 0) {
        throw new Error(`获取访问令牌失败: ${response.data.errmsg}`);
      }

      return response.data.access_token;
    } catch (error) {
      this.logger.error('获取企业微信访问令牌失败', error);
      throw error;
    }
  }

  /**
   * 获取媒体文件信息
   */
  private async getMediaInfo(
    accessToken: string,
    mediaId: string,
  ): Promise<{ fileName?: string; fileSize?: number }> {
    try {
      // 尝试获取文件信息，如果失败则使用默认名称
      const response = await firstValueFrom(
        this.httpService.get(
          `https://qyapi.weixin.qq.com/cgi-bin/media/get?access_token=${accessToken}&media_id=${mediaId}`,
          { responseType: 'stream' },
        ),
      );

      const contentDisposition = response.headers['content-disposition'];
      let fileName: string | undefined;

      if (contentDisposition) {
        const match = contentDisposition.match(/filename="(.+?)"/);
        if (match) {
          fileName = decodeURIComponent(match[1]);
        }
      }

      return {
        fileName,
        fileSize: parseInt(response.headers['content-length'] || '0'),
      };
    } catch (error) {
      this.logger.warn('无法获取媒体文件信息，使用默认设置');
      return {};
    }
  }

  /**
   * 下载文件
   */
  private async downloadFile(url: string, filePath: string): Promise<void> {
    const response = await firstValueFrom(
      this.httpService.get(url, { responseType: 'stream' }),
    );

    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
  }

  /**
   * 生成唯一文件名
   */
  private generateFileName(mediaId: string, originalName?: string): string {
    const timestamp = Date.now();
    const extension = originalName ? path.extname(originalName) : '.bin';
    const baseName = originalName
      ? path.basename(originalName, extension)
      : mediaId;
    
    return `${timestamp}_${baseName}${extension}`;
  }

  /**
   * 对图片进行OCR识别
   */
  async performOCR(filePath: string): Promise<OCRResult> {
    try {
      // 这里集成实际的OCR服务，如腾讯云、阿里云或百度OCR
      // 示例实现 - 实际项目中需要替换为真实的OCR服务
      const ocrResult = await this.callOCRService(filePath);
      
      return {
        text: ocrResult.text,
        confidence: ocrResult.confidence,
        extractedAmount: this.extractAmount(ocrResult.text),
        extractedDate: this.extractDate(ocrResult.text),
        vendor: this.extractVendor(ocrResult.text),
      };
    } catch (error) {
      this.logger.error('OCR识别失败', error);
      throw new Error(`OCR识别失败: ${error.message}`);
    }
  }

  /**
   * 调用OCR服务（示例实现）
   */
  private async callOCRService(filePath: string): Promise<{ text: string; confidence: number }> {
    // 这里应该集成真实的OCR服务
    // 示例返回模拟数据
    this.logger.log(`对文件 ${filePath} 进行OCR识别`);
    
    // 模拟OCR结果
    return {
      text: '餐饮发票 ￥58.50 2024-01-15 星巴克咖啡',
      confidence: 0.95,
    };
  }

  /**
   * 从文本中提取金额
   */
  private extractAmount(text: string): string | undefined {
    // 匹配人民币金额格式
    const patterns = [
      /￥\s*(\d+(?:\.\d{1,2})?)/,
      /¥\s*(\d+(?:\.\d{1,2})?)/,
      /(\d+(?:\.\d{1,2})?)\s*元/,
      /(\d+(?:\.\d{1,2})?)\s*RMB/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return undefined;
  }

  /**
   * 从文本中提取日期
   */
  private extractDate(text: string): string | undefined {
    // 匹配日期格式
    const patterns = [
      /(\d{4}-\d{1,2}-\d{1,2})/,
      /(\d{4}\/\d{1,2}\/\d{1,2})/,
      /(\d{1,2}\/\d{1,2}\/\d{4})/,
      /(\d{4}年\d{1,2}月\d{1,2}日)/,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return undefined;
  }

  /**
   * 从文本中提取商户名称
   */
  private extractVendor(text: string): string | undefined {
    // 常见商户名称关键词
    const vendorKeywords = [
      '星巴克', '麦当劳', '肯德基', '必胜客', '沃尔玛', '家乐福',
      '华润万家', '永辉超市', '盒马鲜生', '叮咚买菜', '美团外卖',
      '饿了么', '滴滴出行', '高德打车', '携程', '去哪儿', '飞猪',
    ];

    for (const keyword of vendorKeywords) {
      if (text.includes(keyword)) {
        return keyword;
      }
    }

    // 如果没有匹配到关键词，返回包含数字和中文的最长连续字符串
    const match = text.match(/[\u4e00-\u9fa5a-zA-Z0-9]{2,}/g);
    if (match) {
      return match[0];
    }

    return undefined;
  }

  /**
   * 清理过期媒体文件
   */
  async cleanupExpiredMedia(maxAgeHours: number = 24): Promise<void> {
    const maxAge = maxAgeHours * 60 * 60 * 1000; // 转换为毫秒
    const now = Date.now();

    try {
      const files = fs.readdirSync(this.mediaDir);
      
      for (const file of files) {
        const filePath = path.join(this.mediaDir, file);
        const stats = fs.statSync(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath);
          this.logger.log(`删除过期媒体文件: ${file}`);
        }
      }
    } catch (error) {
      this.logger.error('清理过期媒体文件失败', error);
    }
  }

  /**
   * 获取媒体文件状态
   */
  async getMediaStatus(mediaId: string): Promise<{
    exists: boolean;
    filePath?: string;
    fileSize?: number;
    lastModified?: Date;
  }> {
    const files = fs.readdirSync(this.mediaDir);
    
    for (const file of files) {
      if (file.includes(mediaId)) {
        const filePath = path.join(this.mediaDir, file);
        const stats = fs.statSync(filePath);
        
        return {
          exists: true,
          filePath,
          fileSize: stats.size,
          lastModified: stats.mtime,
        };
      }
    }

    return { exists: false };
  }
}