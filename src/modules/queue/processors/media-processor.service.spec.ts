import { Test, TestingModule } from '@nestjs/testing';
import { MediaProcessor } from './media-processor.service';
import { WecomMediaService } from '../../wecom/wecom-media.service';
import { OCRService } from '../../ai/ocr.service';
import { VoiceToTextService } from '../../ai/voice-to-text.service';
import { AiService } from '../../ai/ai.service';
import { MediaProcessingJob } from './media-processor.service';
import { BillingInfo } from '../../ai/ai.service';

// Mock fs
jest.mock('fs', () => ({
  readFileSync: jest.fn().mockReturnValue('测试文本内容'),
  existsSync: jest.fn().mockReturnValue(true),
}));

describe('MediaProcessor', () => {
  let processor: MediaProcessor;
  let wecomMediaService: jest.Mocked<WecomMediaService>;
  let ocrService: jest.Mocked<OCRService>;
  let voiceToTextService: jest.Mocked<VoiceToTextService>;
  let aiService: jest.Mocked<AiService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MediaProcessor,
        {
          provide: WecomMediaService,
          useValue: {
            downloadMedia: jest.fn(),
            cleanupExpiredMedia: jest.fn(),
          },
        },
        {
          provide: OCRService,
          useValue: {
            recognize: jest.fn(),
          },
        },
        {
          provide: VoiceToTextService,
          useValue: {
            transcribe: jest.fn(),
            validateAudioFile: jest.fn(),
          },
        },
        {
          provide: AiService,
          useValue: {
            extractBillingInfoFromText: jest.fn(),
          },
        },
      ],
    }).compile();

    processor = module.get<MediaProcessor>(MediaProcessor);
    wecomMediaService = module.get(WecomMediaService);
    ocrService = module.get(OCRService);
    voiceToTextService = module.get(VoiceToTextService);
    aiService = module.get(AiService);
  });

  describe('processImage', () => {
    it('should process image successfully', async () => {
      const mockJob: MediaProcessingJob = {
        mediaId: 'test-image-id',
        messageType: 'image',
        userId: 'test-user-id',
      };

      const mockDownloadResult = {
        filePath: '/test/path/image.jpg',
        fileName: 'test-image.jpg',
        fileSize: 1024,
        mimeType: 'image/jpeg',
        mediaId: 'test-image-id',
      };

      const mockOcrResult = {
        text: '餐饮发票 ￥58.50 2024-01-15 星巴克咖啡',
        confidence: 0.95,
        extractedFields: {
          amount: '58.50',
          date: '2024-01-15',
          vendor: '星巴克咖啡',
        },
        rawData: {},
      };

      const mockBillingInfo: BillingInfo = {
        amount: 58.5,
        category: '餐饮',
        date: '2024-01-15',
        description: '星巴克咖啡',
        type: 'expense',
        error: null,
        confidence: 0.9,
      };

      wecomMediaService.downloadMedia.mockResolvedValue(mockDownloadResult);
      ocrService.recognize.mockResolvedValue(mockOcrResult);
      aiService.extractBillingInfoFromText.mockResolvedValue(mockBillingInfo);

      const result = await processor.processImage(mockJob);

      expect(result).toEqual({
        mediaId: 'test-image-id',
        filePath: '/test/path/image.jpg',
        extractedText: '餐饮发票 ￥58.50 2024-01-15 星巴克咖啡',
        billingInfo: mockBillingInfo,
        processingType: 'image',
      });

      expect(wecomMediaService.downloadMedia).toHaveBeenCalledWith(
        'test-image-id',
        undefined,
        undefined,
      );
      expect(ocrService.recognize).toHaveBeenCalledWith(
        '/test/path/image.jpg',
        'tencent',
      );
      expect(aiService.extractBillingInfoFromText).toHaveBeenCalledWith(
        '餐饮发票 ￥58.50 2024-01-15 星巴克咖啡',
      );
    });

    it('should handle image processing failure', async () => {
      const mockJob: MediaProcessingJob = {
        mediaId: 'test-image-id',
        messageType: 'image',
        userId: 'test-user-id',
      };

      wecomMediaService.downloadMedia.mockRejectedValue(
        new Error('Download failed'),
      );

      await expect(processor.processImage(mockJob)).rejects.toThrow(
        'Download failed',
      );
    });
  });

  describe('processVoice', () => {
    it('should process voice successfully', async () => {
      const mockJob: MediaProcessingJob = {
        mediaId: 'test-voice-id',
        messageType: 'voice',
        userId: 'test-user-id',
      };

      const mockDownloadResult = {
        filePath: '/test/path/voice.mp3',
        fileName: 'test-voice.mp3',
        fileSize: 2048,
        mimeType: 'audio/mpeg',
        mediaId: 'test-voice-id',
      };

      const mockValidation = {
        isValid: true,
        format: 'mp3',
        duration: 10,
        sampleRate: 16000,
      };

      const mockVoiceResult = {
        text: '今天在公司楼下吃了午饭，金额是25元',
        confidence: 0.85,
        segments: [],
        rawData: {},
      };

      const mockBillingInfo: BillingInfo = {
        amount: 25,
        category: '餐饮',
        date: new Date().toISOString().split('T')[0],
        description: '公司楼下午饭',
        type: 'expense',
        error: null,
        confidence: 0.8,
      };

      wecomMediaService.downloadMedia.mockResolvedValue(mockDownloadResult);
      voiceToTextService.validateAudioFile.mockResolvedValue(mockValidation);
      voiceToTextService.transcribe.mockResolvedValue(mockVoiceResult);
      aiService.extractBillingInfoFromText.mockResolvedValue(mockBillingInfo);

      const result = await processor.processVoice(mockJob);

      expect(result).toEqual({
        mediaId: 'test-voice-id',
        filePath: '/test/path/voice.mp3',
        extractedText: '今天在公司楼下吃了午饭，金额是25元',
        billingInfo: mockBillingInfo,
        processingType: 'voice',
      });
    });

    it('should handle invalid audio file', async () => {
      const mockJob: MediaProcessingJob = {
        mediaId: 'test-voice-id',
        messageType: 'voice',
        userId: 'test-user-id',
      };

      const mockDownloadResult = {
        filePath: '/test/path/voice.mp3',
        fileName: 'test-voice.mp3',
        fileSize: 0,
        mimeType: 'audio/mpeg',
        mediaId: 'test-voice-id',
      };

      const mockValidation = {
        isValid: false,
        error: '空文件',
      };

      wecomMediaService.downloadMedia.mockResolvedValue(mockDownloadResult);
      voiceToTextService.validateAudioFile.mockResolvedValue(mockValidation);

      await expect(processor.processVoice(mockJob)).rejects.toThrow(
        '音频文件验证失败: 空文件',
      );
    });
  });

  describe('processFile', () => {
    it('should process image file as image', async () => {
      const mockJob: MediaProcessingJob = {
        mediaId: 'test-file-id',
        messageType: 'file',
        userId: 'test-user-id',
      };

      const mockDownloadResult = {
        filePath: '/test/path/document.jpg',
        fileName: 'document.jpg',
        fileSize: 1024,
        mimeType: 'image/jpeg',
        mediaId: 'test-file-id',
      };

      const mockOcrResult = {
        text: '办公用品 ￥120.00 2024-01-15 文具店',
        confidence: 0.9,
        extractedFields: {},
        rawData: {},
      };

      const mockBillingInfo: BillingInfo = {
        amount: 120,
        category: '办公',
        date: '2024-01-15',
        description: '文具店购买办公用品',
        type: 'expense',
        error: null,
        confidence: 0.85,
      };

      wecomMediaService.downloadMedia.mockResolvedValue(mockDownloadResult);
      ocrService.recognize.mockResolvedValue(mockOcrResult);
      aiService.extractBillingInfoFromText.mockResolvedValue(mockBillingInfo);

      const result = await processor.processFile(mockJob);

      expect(result.processingType).toBe('file');
      expect(ocrService.recognize).toHaveBeenCalled();
    });

    it('should process text file as text', async () => {
      const mockJob: MediaProcessingJob = {
        mediaId: 'test-file-id',
        messageType: 'file',
        userId: 'test-user-id',
      };

      const mockDownloadResult = {
        filePath: '/test/path/document.txt',
        fileName: 'document.txt',
        fileSize: 512,
        mimeType: 'text/plain',
        mediaId: 'test-file-id',
      };

      const mockBillingInfo: BillingInfo = {
        amount: 50,
        category: '交通',
        date: new Date().toISOString().split('T')[0],
        description: '出租车费用',
        type: 'expense',
        error: null,
        confidence: 0.9,
      };

      wecomMediaService.downloadMedia.mockResolvedValue(mockDownloadResult);
      aiService.extractBillingInfoFromText.mockResolvedValue(mockBillingInfo);

      const result = await processor.processFile(mockJob);

      expect(result.processingType).toBe('file');
      expect(result.extractedText).toBe('测试文本内容');
    });
  });

  describe('utility methods', () => {
    it('should calculate priority correctly', () => {
      const imageJob: MediaProcessingJob = {
        mediaId: 'test',
        messageType: 'image',
        userId: 'test',
      };
      
      const voiceJob: MediaProcessingJob = {
        mediaId: 'test',
        messageType: 'voice',
        userId: 'test',
      };
      
      const fileJob: MediaProcessingJob = {
        mediaId: 'test',
        messageType: 'file',
        userId: 'test',
      };

      expect(processor.calculatePriority(imageJob)).toBe(3);
      expect(processor.calculatePriority(voiceJob)).toBe(2);
      expect(processor.calculatePriority(fileJob)).toBe(1);
    });

    it('should return supported media types', () => {
      const types = processor.getSupportedMediaTypes();
      expect(types).toEqual(['image', 'voice', 'file']);
    });

    it('should return supported file formats', () => {
      const formats = processor.getSupportedFileFormats();
      expect(formats).toHaveProperty('images');
      expect(formats).toHaveProperty('audio');
      expect(formats).toHaveProperty('documents');
    });
  });

  describe('error handling', () => {
    it('should handle processing error gracefully', async () => {
      const mockJob: MediaProcessingJob = {
        mediaId: 'test-error',
        messageType: 'image',
        userId: 'test-user-id',
      };

      const error = new Error('Test error');
      wecomMediaService.downloadMedia.mockRejectedValue(error);

      await expect(processor.processImage(mockJob)).rejects.toThrow('Test error');
    });
  });

  describe('cleanup', () => {
    it('should cleanup expired media', async () => {
      await processor.cleanupMedia();
      expect(wecomMediaService.cleanupExpiredMedia).toHaveBeenCalledWith(24);
    });
  });
});