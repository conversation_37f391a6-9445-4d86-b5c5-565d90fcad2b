import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import { WecomMediaService } from '../../wecom/wecom-media.service';
import { OCRService } from '../../ai/ocr.service';
import { VoiceToTextService } from '../../ai/voice-to-text.service';
import { AiService } from '../../ai/ai.service';
import { BillingInfo } from '../../ai/ai.service';

export interface MediaProcessingJob {
  mediaId: string;
  messageType: 'image' | 'voice' | 'file';
  userId: string;
  corpId?: string;
  corpSecret?: string;
  originalContent?: string;
}

export interface MediaProcessingResult {
  mediaId: string;
  filePath: string;
  extractedText?: string;
  billingInfo?: BillingInfo;
  processingType: 'image' | 'voice' | 'file';
}

@Injectable()
export class MediaProcessor {
  private readonly logger = new Logger(MediaProcessor.name);

  constructor(
    private readonly wecomMediaService: WecomMediaService,
    private readonly ocrService: OCRService,
    private readonly voiceToTextService: VoiceToTextService,
    private readonly aiService: AiService,
  ) {}

  async processImage(job: MediaProcessingJob): Promise<MediaProcessingResult> {
    const { mediaId, userId, corpId, corpSecret } = job;
    
    try {
      this.logger.log(`开始处理图片: ${mediaId}`);

      const downloadResult = await this.wecomMediaService.downloadMedia(
        mediaId,
        corpId,
        corpSecret,
      );

      const ocrResult = await this.ocrService.recognize(downloadResult.filePath, 'tencent');
      
      const billingInfo = await this.aiService.extractBillingInfoFromText(ocrResult.text);

      return {
        mediaId,
        filePath: downloadResult.filePath,
        extractedText: ocrResult.text,
        billingInfo,
        processingType: 'image',
      };
    } catch (error) {
      this.logger.error(`图片处理失败: ${mediaId}`, error);
      throw error;
    }
  }

  async processVoice(job: MediaProcessingJob): Promise<MediaProcessingResult> {
    const { mediaId, userId, corpId, corpSecret } = job;
    
    try {
      this.logger.log(`开始处理语音: ${mediaId}`);

      const downloadResult = await this.wecomMediaService.downloadMedia(
        mediaId,
        corpId,
        corpSecret,
      );

      const validation = await this.voiceToTextService.validateAudioFile(downloadResult.filePath);
      if (!validation.isValid) {
        throw new Error(`音频文件验证失败: ${validation.error}`);
      }

      const voiceResult = await this.voiceToTextService.transcribe(
        downloadResult.filePath,
        'tencent',
        {
          format: validation.format,
          sampleRate: validation.sampleRate,
        },
      );

      const billingInfo = await this.aiService.extractBillingInfoFromText(voiceResult.text);

      return {
        mediaId,
        filePath: downloadResult.filePath,
        extractedText: voiceResult.text,
        billingInfo,
        processingType: 'voice',
      };
    } catch (error) {
      this.logger.error(`语音处理失败: ${mediaId}`, error);
      throw error;
    }
  }

  async processFile(job: MediaProcessingJob): Promise<MediaProcessingResult> {
    const { mediaId, userId, corpId, corpSecret } = job;
    
    try {
      this.logger.log(`开始处理文件: ${mediaId}`);

      const downloadResult = await this.wecomMediaService.downloadMedia(
        mediaId,
        corpId,
        corpSecret,
      );

      let extractedText = '';
      let billingInfo: BillingInfo | undefined;

      if (downloadResult.mimeType.startsWith('image/')) {
        const ocrResult = await this.ocrService.recognize(downloadResult.filePath, 'tencent');
        extractedText = ocrResult.text;
        billingInfo = await this.aiService.extractBillingInfoFromText(extractedText);
      } else if (downloadResult.mimeType.startsWith('text/')) {
        extractedText = fs.readFileSync(downloadResult.filePath, 'utf-8');
        billingInfo = await this.aiService.extractBillingInfoFromText(extractedText);
      } else if (downloadResult.mimeType.includes('pdf')) {
        extractedText = await this.extractPdfText(downloadResult.filePath);
        billingInfo = await this.aiService.extractBillingInfoFromText(extractedText);
      } else {
        this.logger.warn(`不支持的文件类型: ${downloadResult.mimeType}`);
        extractedText = `文件类型: ${downloadResult.mimeType}，无法自动提取内容`;
        billingInfo = await this.aiService.extractBillingInfoFromText(extractedText);
      }

      return {
        mediaId,
        filePath: downloadResult.filePath,
        extractedText,
        billingInfo,
        processingType: 'file',
      };
    } catch (error) {
      this.logger.error(`文件处理失败: ${mediaId}`, error);
      throw error;
    }
  }

  async cleanupMedia(): Promise<void> {
    try {
      this.logger.log('开始清理过期媒体文件');
      await this.wecomMediaService.cleanupExpiredMedia(24);
      this.logger.log('媒体文件清理完成');
    } catch (error) {
      this.logger.error('媒体文件清理失败', error);
      throw error;
    }
  }

  private async extractPdfText(filePath: string): Promise<string> {
    this.logger.warn(`PDF文本提取未实现: ${filePath}`);
    return 'PDF文件内容已上传，但暂时无法自动提取文本内容';
  }

  calculatePriority(job: MediaProcessingJob): number {
    const priorities = {
      image: 3,
      voice: 2,
      file: 1,
    };
    return priorities[job.messageType] || 1;
  }

  getSupportedMediaTypes(): string[] {
    return ['image', 'voice', 'file'];
  }

  getSupportedFileFormats() {
    return {
      images: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
      audio: ['mp3', 'wav', 'm4a', 'amr', 'aac', 'ogg'],
      documents: ['pdf', 'txt', 'doc', 'docx', 'xls', 'xlsx'],
    };
  }

  async batchProcessMedia(
    jobs: MediaProcessingJob[],
  ): Promise<{ success: number; failed: number; results: MediaProcessingResult[] }> {
    let success = 0;
    let failed = 0;
    const results: MediaProcessingResult[] = [];

    for (const job of jobs) {
      try {
        let result: MediaProcessingResult;
        
        switch (job.messageType) {
          case 'image':
            result = await this.processImage(job);
            break;
          case 'voice':
            result = await this.processVoice(job);
            break;
          case 'file':
            result = await this.processFile(job);
            break;
          default:
            throw new Error(`不支持的媒体类型: ${job.messageType}`);
        }

        results.push(result);
        success++;
      } catch (error) {
        this.logger.error(`批量处理失败: ${job.mediaId}`, error);
        failed++;
      }
    }

    return { success, failed, results };
  }
}