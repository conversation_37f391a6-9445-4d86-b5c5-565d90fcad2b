import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class UserService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createUserDto: CreateUserDto) {
    return await this.prisma.user.create({
      data: {
        id: createUserDto.wecomUserId,
        name: createUserDto.name,
      },
    });
  }

  async findAll() {
    return await this.prisma.user.findMany();
  }

  async findOne(id: string) {
    return await this.prisma.user.findUnique({
      where: { id },
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    return await this.prisma.user.update({
      where: { id },
      data: updateUserDto,
    });
  }

  async remove(id: string) {
    return await this.prisma.user.delete({
      where: { id },
    });
  }

  /**
   * 设置用户状态
   * @param userId 用户ID
   * @param state 状态值: awaiting_binding_notion | awaiting_binding_feishu
   * @throws 当用户不存在时抛出 NotFoundException
   */
  async setUserState(
    userId: string,
    state: 'awaiting_binding_notion' | 'awaiting_binding_feishu',
  ): Promise<void> {
    try {
      await this.prisma.user.update({
        where: { id: userId },
        data: { state: state },
      });
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025'
      ) {
        throw new NotFoundException(`用户 ${userId} 不存在`);
      }
      throw error;
    }
  }
  /**
   * 增加用户使用次数
   * @param userId 用户ID
   */
  async incrementUsage(userId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: { usageCount: { increment: 1 } } as Prisma.UserUpdateInput,
    });
  }
}
