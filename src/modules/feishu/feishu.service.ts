import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as lark from '@larksuiteoapi/node-sdk';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { PrismaService } from '../../prisma/prisma.service';
import { BillingInfo } from '../ai/ai.service';
import { encryptData, decryptData } from '../../utils/crypto.util';

export interface FeishuAuthToken {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  tenant_key?: string;
}

interface FeishuTokenResponse {
  code: number;
  msg: string;
  data: {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
    scope: string;
  };
}

export interface FeishuApp {
  app_id: string;
  app_name: string;
  app_token: string;
}

export interface FeishuTable {
  table_id: string;
  table_name: string;
  fields: FeishuField[];
}

export interface FeishuField {
  field_id: string;
  field_name: string;
  field_type: string;
}

export interface CreateFeishuBindingDto {
  userId: string;
  accessToken: string;
  refreshToken: string;
  appToken: string;
  tableId: string;
  tableName?: string;
}

@Injectable()
export class FeishuService {
  private readonly logger = new Logger(FeishuService.name);
  private client: any;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly prisma: PrismaService,
  ) {
    this.initClient();
  }

  private initClient() {
    try {
      const appId = this.configService.get<string>('FEISHU_APP_ID');
      const appSecret = this.configService.get<string>('FEISHU_APP_SECRET');

      if (!appId || !appSecret) {
        this.logger.warn('飞书配置不完整，跳过初始化');
        return;
      }

      this.client = new lark.Client({
        appId,
        appSecret,
        domain: lark.Domain.Feishu,
      });
      this.logger.log('飞书客户端初始化成功');
    } catch (error) {
      this.logger.error('初始化飞书客户端失败', error);
      // 不抛出错误，允许应用继续启动
      this.logger.warn('飞书服务将不可用');
    }
  }

  /**
   * 获取用户的应用列表
   */
  async getUserApps(accessToken: string): Promise<FeishuApp[]> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(
          'https://open.feishu.cn/open-apis/bitable/v1/apps',
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          },
        ),
      );

      return data.data.items.map((app: any) => ({
        app_id: app.app_id,
        app_name: app.app_name,
        app_token: app.app_token,
      }));
    } catch (error) {
      this.logger.error('获取飞书应用列表失败:', error);
      throw new Error('Failed to fetch apps');
    }
  }

  /**
   * 获取应用的表格列表
   */
  async getAppTables(
    accessToken: string,
    appToken: string,
  ): Promise<FeishuTable[]> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(
          `https://open.feishu.cn/open-apis/bitable/v1/apps/${appToken}/tables`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          },
        ),
      );

      const tables = await Promise.all(
        data.data.items.map(async (table: any) => {
          const fields = await this.getTableFields(
            accessToken,
            appToken,
            table.table_id,
          );
          return {
            table_id: table.table_id,
            table_name: table.name,
            fields,
          };
        }),
      );

      return tables;
    } catch (error) {
      this.logger.error('获取飞书表格列表失败:', error);
      throw new Error('Failed to fetch tables');
    }
  }

  /**
   * 获取表格字段
   */
  async getTableFields(
    accessToken: string,
    appToken: string,
    tableId: string,
  ): Promise<FeishuField[]> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.get(
          `https://open.feishu.cn/open-apis/bitable/v1/apps/${appToken}/tables/${tableId}/fields`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          },
        ),
      );

      return data.data.items.map((field: any) => ({
        field_id: field.field_id,
        field_name: field.field_name,
        field_type: field.type,
      }));
    } catch (error) {
      this.logger.error('获取飞书表格字段失败:', error);
      throw new Error('Failed to fetch table fields');
    }
  }
  /**
   * 获取飞书OAuth授权URL
   */
  getAuthUrl(state?: string): string {
    const clientId = this.configService.get<string>('FEISHU_APP_ID')!;
    const redirectUri = this.configService.get<string>('FEISHU_REDIRECT_URI')!;

    const params = new URLSearchParams({
      redirect_uri: redirectUri,
      app_id: clientId,
      state: state || 'default',
    });

    return `https://open.feishu.cn/open-apis/authen/v1/index?${params.toString()}`;
  }

  /**
   * 处理飞书OAuth回调
   * @param code 授权码
   */
  async handleAuthCallback(code: string): Promise<FeishuAuthToken> {
    const clientId = this.configService.get<string>('FEISHU_APP_ID')!;
    const clientSecret = this.configService.get<string>('FEISHU_APP_SECRET')!;
    const redirectUri = this.configService.get<string>('FEISHU_REDIRECT_URI')!;

    try {
      const response = await firstValueFrom(
        this.httpService.post<FeishuTokenResponse>(
          'https://open.feishu.cn/open-apis/authen/v1/access_token',
          {
            grant_type: 'authorization_code',
            code,
            client_id: clientId,
            client_secret: clientSecret,
            redirect_uri: redirectUri,
          },
        ),
      );

      if (response.data.code !== 0) {
        throw new Error(`飞书OAuth失败: ${response.data.msg}`);
      }

      return {
        access_token: response.data.data.access_token,
        refresh_token: response.data.data.refresh_token,
        expires_in: response.data.data.expires_in,
        token_type: response.data.data.token_type,
      };
    } catch (error) {
      this.logger.error('获取飞书access token失败', error);
      throw error;
    }
  }

  /**
   * 创建绑定关系
   */
  async createBinding(dto: CreateFeishuBindingDto): Promise<void> {
    const encryptedToken = encryptData(dto.accessToken);
    const encryptedRefreshToken = encryptData(dto.refreshToken);

    await this.prisma.binding.create({
      data: {
        user_id: dto.userId,
        type: 'FEISHU',
        access_token: encryptedToken,
        refresh_token: encryptedRefreshToken,
        target_id: dto.tableId,
        target_name: dto.tableName,
        is_active: true,
      },
    });

    // 将其他飞书绑定设为非活跃
    await this.prisma.binding.updateMany({
      where: {
        user_id: dto.userId,
        type: 'FEISHU',
        target_id: { not: dto.tableId },
      },
      data: {
        is_active: false,
      },
    });
  }

  /**
   * 添加记录到飞书多维表格
   */
  /**
   * 刷新飞书访问令牌
   */
  async refreshAccessToken(refreshToken: string): Promise<{
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
  }> {
    try {
      const { data: response } = await firstValueFrom(
        this.httpService.post(
          'https://open.feishu.cn/open-apis/authen/v1/refresh_access_token',
          {
            grant_type: 'refresh_token',
            refresh_token: refreshToken,
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      if (response.code !== 0) {
        this.logger.error('刷新飞书access token失败', response);
        throw new Error(`刷新飞书access token失败: ${response.msg}`);
      }

      return {
        access_token: response.data.access_token,
        refresh_token: response.data.refresh_token,
        expires_in: response.data.expires_in,
        token_type: response.data.token_type,
      };
    } catch (error) {
      this.logger.error('刷新飞书access token失败', error);
      throw error;
    }
  }

  /**
   * 更新绑定中的访问令牌
   */
  async updateBindingTokens(
    userId: string,
    accessToken: string,
    refreshToken: string,
  ): Promise<void> {
    const encryptedAccessToken = encryptData(accessToken);
    const encryptedRefreshToken = encryptData(refreshToken);

    await this.prisma.binding.updateMany({
      where: {
        user_id: userId,
        type: 'FEISHU',
        is_active: true,
      },
      data: {
        access_token: encryptedAccessToken,
        refresh_token: encryptedRefreshToken,
        updated_at: new Date(),
      },
    });
  }

  /**
   * 添加记录到飞书多维表格，支持自动刷新令牌
   */
  async addRecordToTableWithRetry(
    userId: string,
    appToken: string,
    tableId: string,
    data: BillingInfo,
  ): Promise<any> {
    const binding = await this.prisma.binding.findFirst({
      where: {
        user_id: userId,
        type: 'FEISHU',
        is_active: true,
      },
    });
    if (!binding) {
      throw new Error('未找到用户的飞书绑定');
    }

    const accessToken = decryptData(binding.access_token);
    const refreshToken = binding.refresh_token
      ? decryptData(binding.refresh_token)
      : null;

    try {
      return await this.addRecordToTable(accessToken, appToken, tableId, data);
    } catch (error) {
      // 如果令牌过期，尝试刷新
      if (error.response?.status === 401 && refreshToken) {
        this.logger.log('令牌过期，尝试刷新...');
        try {
          const newTokens = await this.refreshAccessToken(refreshToken);
          await this.updateBindingTokens(
            userId,
            newTokens.access_token,
            newTokens.refresh_token,
          );

          // 使用新令牌重试
          return await this.addRecordToTable(
            newTokens.access_token,
            appToken,
            tableId,
            data,
          );
        } catch (refreshError) {
          this.logger.error('令牌刷新失败', refreshError);
          throw new Error('飞书令牌已过期，请重新授权');
        }
      }
      throw error;
    }
  }

  async addRecordToTable(
    accessToken: string,
    appToken: string,
    tableId: string,
    data: BillingInfo,
  ): Promise<any> {
    try {
      // 构建记录数据
      const fields = {
        描述: data.description,
        金额: data.amount,
        分类: data.category,
        日期: new Date(data.date).getTime(), // 飞书日期字段需要时间戳
      };

      const { data: result } = await firstValueFrom(
        this.httpService.post(
          `https://open.feishu.cn/open-apis/bitable/v1/apps/${appToken}/tables/${tableId}/records`,
          {
            fields,
          },
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log('成功添加记录到飞书表格');
      return result;
    } catch (error) {
      this.logger.error('添加记录到飞书表格失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户的活跃飞书绑定
   */
  async getActiveBinding(userId: string) {
    const binding = await this.prisma.binding.findFirst({
      where: {
        user_id: userId,
        type: 'FEISHU',
        is_active: true,
      },
    });

    if (!binding) {
      return null;
    }

    return {
      ...binding,
      access_token: decryptData(binding.access_token),
      refresh_token: binding.refresh_token
        ? decryptData(binding.refresh_token)
        : null,
    };
  }

  /**
   * 同步记账数据到飞书
   */
  async syncBillingData(
    userId: string,
    billingInfo: BillingInfo,
  ): Promise<void> {
    const binding = await this.getActiveBinding(userId);

    if (!binding) {
      throw new Error('用户未绑定飞书账户');
    }

    // 从target_id中解析appToken和tableId
    const [appToken, tableId] = binding.target_id.split(':');

    await this.addRecordToTable(
      binding.access_token,
      appToken,
      tableId,
      billingInfo,
    );
  }

  /**
   * 添加内容到飞书文档 (保留原方法)
   * @param documentId 文档ID
   * @param content 要添加的内容(Markdown格式)
   */
  async appendToDocument(documentId: string, content: string): Promise<any> {
    try {
      const result = await this.client.documents.appendContent({
        document_id: documentId,
        content: content,
        content_type: 'markdown',
      });
      return result;
    } catch (error) {
      this.logger.error('添加记录到飞书文档失败', error);
      throw error;
    }
  }
}
