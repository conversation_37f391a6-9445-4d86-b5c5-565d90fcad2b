import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Cron, CronExpression } from '@nestjs/schedule';

export interface UsageStats {
  userId: string;
  yearMonth: number;
  count: number;
  limit: number;
  remaining: number;
  percentage: number;
}

@Injectable()
export class UsageService {
  private readonly logger = new Logger(UsageService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 获取当前年月
   */
  private getCurrentYearMonth(): number {
    const now = new Date();
    return now.getFullYear() * 100 + (now.getMonth() + 1);
  }

  /**
   * 增加用户使用次数
   */
  async incrementUsage(userId: string): Promise<void> {
    const yearMonth = this.getCurrentYearMonth();

    await this.prisma.usageStat.upsert({
      where: {
        user_id_year_month: {
          user_id: userId,
          year_month: yearMonth,
        },
      },
      update: {
        count: { increment: 1 },
      },
      create: {
        user_id: userId,
        year_month: yearMonth,
        count: 1,
      },
    });

    this.logger.log(`用户 ${userId} 使用次数已增加`);
  }

  /**
   * 获取用户当月使用统计
   */
  async getCurrentUsage(userId: string): Promise<UsageStats> {
    const yearMonth = this.getCurrentYearMonth();

    const usageStat = await this.prisma.usageStat.findUnique({
      where: {
        user_id_year_month: {
          user_id: userId,
          year_month: yearMonth,
        },
      },
    });

    const count = usageStat?.count || 0;

    // 获取用户限额
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: {
          where: {
            status: 'active',
            end_date: { gte: new Date() },
          },
          orderBy: { end_date: 'desc' },
          take: 1,
        },
      },
    });

    let limit = 30; // 默认免费限额
    if (user?.subscriptions && user.subscriptions.length > 0) {
      limit = user.subscriptions[0].limit;
    }

    const remaining = Math.max(0, limit - count);
    const percentage = limit > 0 ? (count / limit) * 100 : 0;

    return {
      userId,
      yearMonth,
      count,
      limit,
      remaining,
      percentage,
    };
  }

  /**
   * 检查用户是否超出限额
   */
  async isOverLimit(userId: string): Promise<boolean> {
    const usage = await this.getCurrentUsage(userId);
    return usage.count >= usage.limit;
  }

  /**
   * 获取用户历史使用统计
   */
  async getUsageHistory(userId: string, months: number = 12) {
    const currentYearMonth = this.getCurrentYearMonth();
    const startYearMonth = this.calculatePreviousYearMonth(
      currentYearMonth,
      months,
    );

    return await this.prisma.usageStat.findMany({
      where: {
        user_id: userId,
        year_month: {
          gte: startYearMonth,
          lte: currentYearMonth,
        },
      },
      orderBy: { year_month: 'desc' },
    });
  }

  /**
   * 计算前N个月的年月值
   */
  private calculatePreviousYearMonth(
    currentYearMonth: number,
    months: number,
  ): number {
    const year = Math.floor(currentYearMonth / 100);
    const month = currentYearMonth % 100;

    let targetYear = year;
    let targetMonth = month - months;

    while (targetMonth <= 0) {
      targetMonth += 12;
      targetYear -= 1;
    }

    return targetYear * 100 + targetMonth;
  }

  /**
   * 重置所有用户的月度使用统计（定时任务）
   */
  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  async resetMonthlyUsage(): Promise<void> {
    this.logger.log('开始重置月度使用统计...');

    try {
      // 这里不需要删除数据，因为我们使用年月作为分区
      // 新的月份会自动创建新的记录
      this.logger.log('月度使用统计重置完成');
    } catch (error) {
      this.logger.error('重置月度使用统计失败:', error);
    }
  }

  /**
   * 获取系统总体使用统计
   */
  async getSystemStats() {
    const currentYearMonth = this.getCurrentYearMonth();

    const totalUsers = await this.prisma.user.count();
    const activeUsers = await this.prisma.usageStat.count({
      where: {
        year_month: currentYearMonth,
        count: { gt: 0 },
      },
    });

    const totalUsage = await this.prisma.usageStat.aggregate({
      where: { year_month: currentYearMonth },
      _sum: { count: true },
    });

    const activeSubscriptions = await this.prisma.subscription.count({
      where: {
        status: 'active',
        end_date: { gte: new Date() },
      },
    });

    return {
      totalUsers,
      activeUsers,
      totalUsage: totalUsage._sum.count || 0,
      activeSubscriptions,
      currentMonth: currentYearMonth,
    };
  }

  /**
   * 清理旧的使用统计数据
   */
  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  async cleanupOldStats(): Promise<void> {
    const cutoffYearMonth = this.calculatePreviousYearMonth(
      this.getCurrentYearMonth(),
      24,
    ); // 保留24个月

    try {
      const deleted = await this.prisma.usageStat.deleteMany({
        where: {
          year_month: { lt: cutoffYearMonth },
        },
      });

      this.logger.log(`清理了 ${deleted.count} 条旧的使用统计记录`);
    } catch (error) {
      this.logger.error('清理旧统计数据失败:', error);
    }
  }
}
