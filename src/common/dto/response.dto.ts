export class ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
  requestId?: string;

  constructor(
    success: boolean,
    message: string,
    data?: T,
    error?: string,
    requestId?: string,
  ) {
    this.success = success;
    this.message = message;
    this.data = data;
    this.error = error;
    this.timestamp = new Date().toISOString();
    this.requestId = requestId;
  }

  static success<T>(
    data?: T,
    message = 'Success',
    requestId?: string,
  ): ApiResponse<T> {
    return new ApiResponse(true, message, data, undefined, requestId);
  }

  static error(
    message: string,
    error?: string,
    requestId?: string,
  ): ApiResponse {
    return new ApiResponse(false, message, undefined, error, requestId);
  }
}
