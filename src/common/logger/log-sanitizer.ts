/**
 * 日志敏感数据清理工具
 * 用于清理日志中的敏感信息，防止数据泄露
 */

export class LogSanitizer {
  private static readonly sensitivePatterns = [
    // 访问令牌
    /"access_token":\s*"([^"]+)"/gi,
    /access_token=([^&\s]+)/gi,
    /token=([^&\s]+)/gi,

    // 刷新令牌
    /"refresh_token":\s*"([^"]+)"/gi,
    /refresh_token=([^&\s]+)/gi,

    // API密钥
    /"api_key":\s*"([^"]+)"/gi,
    /api_key=([^&\s]+)/gi,

    // 加密密钥
    /"encryption_key":\s*"([^"]+)"/gi,
    /encryption_key=([^&\s]+)/gi,

    // 密码
    /"password":\s*"([^"]+)"/gi,
    /password=([^&\s]+)/gi,

    // 私钥
    /"private_key":\s*"([^"]+)"/gi,
    /private_key=([^&\s]+)/gi,

    // 签名
    /"signature":\s*"([^"]+)"/gi,
    /signature=([^&\s]+)/gi,

    // 会话ID
    /"session_id":\s*"([^"]+)"/gi,
    /session_id=([^&\s]+)/gi,

    // 授权头
    /Authorization:\s*Bearer\s+([^\s]+)/gi,
    /Authorization:\s*Basic\s+([^\s]+)/gi,
  ];

  private static readonly replacement = '[REDACTED]';

  /**
   * 清理日志内容中的敏感信息
   * @param content 原始日志内容
   * @returns 清理后的日志内容
   */
  static sanitize(content: string): string {
    if (!content || typeof content !== 'string') {
      return content;
    }

    let sanitized = content;

    // 应用所有敏感模式
    this.sensitivePatterns.forEach((pattern) => {
      sanitized = sanitized.replace(pattern, (match) => {
        // 保留前4个字符，其余用星号替换
        if (match.length > 10) {
          const prefix = match.substring(0, 10);
          return prefix + '***' + this.replacement + '***';
        }
        return this.replacement;
      });
    });

    return sanitized;
  }

  /**
   * 清理对象中的敏感信息
   * @param obj 要清理的对象
   * @returns 清理后的对象副本
   */
  static sanitizeObject(obj: any): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const sanitized = JSON.parse(JSON.stringify(obj));
    this.sanitizeObjectRecursive(sanitized);
    return sanitized;
  }

  private static sanitizeObjectRecursive(obj: any): void {
    if (!obj || typeof obj !== 'object') {
      return;
    }

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const value = obj[key];

        // 如果是敏感字段，清理值
        if (this.isSensitiveField(key)) {
          if (typeof value === 'string') {
            obj[key] = this.maskString(value);
          } else if (typeof value === 'object') {
            obj[key] = '[OBJECT_REDACTED]';
          } else {
            obj[key] = '[REDACTED]';
          }
        } else if (typeof value === 'object') {
          // 递归处理嵌套对象
          this.sanitizeObjectRecursive(value);
        }
      }
    }
  }

  private static isSensitiveField(key: string): boolean {
    const sensitiveFields = [
      'access_token',
      'refresh_token',
      'api_key',
      'api_secret',
      'encryption_key',
      'private_key',
      'password',
      'secret',
      'signature',
      'auth_code',
      'authorization',
      'token',
      'session_id',
      'session_token',
      'bearer',
      'basic',
    ];

    const lowerKey = key.toLowerCase();
    return sensitiveFields.some((field) => lowerKey.includes(field));
  }

  private static maskString(str: string): string {
    if (!str || str.length <= 4) {
      return '****';
    }

    // 对于较长的字符串，保留首尾字符
    const first = str.substring(0, 4);
    const last = str.substring(str.length - 4);
    return `${first}***${last}`;
  }

  /**
   * 清理URL中的敏感参数
   * @param url 原始URL
   * @returns 清理后的URL
   */
  static sanitizeUrl(url: string): string {
    if (!url) return url;

    try {
      const urlObj = new URL(url);
      const params = urlObj.searchParams;

      // 清理敏感参数
      [
        'access_token',
        'refresh_token',
        'token',
        'api_key',
        'signature',
      ].forEach((param) => {
        if (params.has(param)) {
          params.set(param, '[REDACTED]');
        }
      });

      return urlObj.toString();
    } catch {
      return url;
    }
  }

  /**
   * 清理错误消息中的敏感信息
   * @param error 错误对象
   * @returns 清理后的错误对象
   */
  static sanitizeError(error: any): any {
    if (!error) return error;

    const sanitized: any = {};

    // 保留基本错误信息
    if (error.message) {
      sanitized.message = this.sanitize(error.message);
    }

    if (error.name) {
      sanitized.name = error.name;
    }

    if (error.status) {
      sanitized.status = error.status;
    }

    if (error.stack) {
      sanitized.stack = this.sanitize(error.stack);
    }

    // 清理其他属性
    for (const key in error) {
      if (
        error.hasOwnProperty(key) &&
        !['message', 'name', 'status', 'stack'].includes(key)
      ) {
        const value = error[key];
        if (typeof value === 'string') {
          sanitized[key] = this.sanitize(value);
        } else if (typeof value === 'object') {
          sanitized[key] = this.sanitizeObject(value);
        } else {
          sanitized[key] = value;
        }
      }
    }

    return sanitized;
  }
}
