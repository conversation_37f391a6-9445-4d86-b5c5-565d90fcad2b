import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Request, Response } from 'express';
import { LogSanitizer } from '../logger/log-sanitizer';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const { method, url, ip } = request;
    const userAgent = request.get('User-Agent') || '';

    const now = Date.now();

    return next.handle().pipe(
      tap(() => {
        const { statusCode } = response;
        const contentLength = response.get('content-length');
        const responseTime = Date.now() - now;

        // 清理敏感信息
        const sanitizedUrl = LogSanitizer.sanitizeUrl(url);
        const sanitizedUserAgent = LogSanitizer.sanitize(userAgent);

        this.logger.log(
          `${method} ${sanitizedUrl} ${statusCode} ${contentLength || 0}b - ${responseTime}ms - ${ip} - ${sanitizedUserAgent}`,
        );
      }),
    );
  }
}
