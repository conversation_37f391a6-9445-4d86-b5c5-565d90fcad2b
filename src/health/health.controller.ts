import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { PrismaService } from '../prisma/prisma.service';
import { FileLoggerService } from '../common/logger/file-logger.service';

@ApiTags('健康检查')
@Controller('health')
export class HealthController {
  constructor(
    private readonly prisma: PrismaService,
    private readonly fileLogger: FileLoggerService,
  ) {}

  @ApiOperation({
    summary: '基础健康检查',
    description: '检查应用是否正常运行',
  })
  @Get()
  async check() {
    // 测试文件日志功能
    this.fileLogger.logBusiness(
      '健康检查请求',
      {
        endpoint: '/health',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      },
      'HealthController',
    );

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
  }

  @ApiOperation({
    summary: '数据库健康检查',
    description: '检查数据库连接是否正常',
  })
  @Get('db')
  async checkDatabase() {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return {
        status: 'ok',
        database: 'connected',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      // 记录数据库连接错误
      this.fileLogger.error('数据库连接失败', error.stack, 'HealthController');

      return {
        status: 'error',
        database: 'disconnected',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @ApiOperation({
    summary: '详细健康检查',
    description: '检查所有系统组件的健康状态',
  })
  @Get('detailed')
  async detailedCheck() {
    const checks = {
      database: 'unknown',
      memory: 'unknown',
      disk: 'unknown',
    };

    // 检查数据库
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      checks.database = 'healthy';
    } catch {
      checks.database = 'unhealthy';
    }

    // 检查内存使用
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent =
      (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    checks.memory = memoryUsagePercent < 90 ? 'healthy' : 'warning';

    // 简单的磁盘检查（这里只是示例）
    checks.disk = 'healthy';

    const overallStatus = Object.values(checks).every(
      (status) => status === 'healthy',
    )
      ? 'healthy'
      : 'degraded';

    return {
      status: overallStatus,
      checks,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
    };
  }
}
